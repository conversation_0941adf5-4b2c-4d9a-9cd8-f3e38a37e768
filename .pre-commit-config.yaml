# Root-level pre-commit configuration for the entire monorepo
# See https://pre-commit.com for more information
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
      - id: debug-statements
      - id: check-docstring-first

  # li_agentcore Package hooks
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff
        name: ruff-li_agentcore
        args: [--fix, --exit-non-zero-on-fix]
        files: ^packages/li_agentcore/
        exclude: ^packages/li_agentcore/\.venv/
      - id: ruff-format
        name: ruff-format-li_agentcore
        files: ^packages/li_agentcore/
        exclude: ^packages/li_agentcore/\.venv/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.0
    hooks:
      - id: mypy
        name: mypy-li_agentcore
        additional_dependencies: [pydantic]
        args: [--ignore-missing-imports]
        files: ^packages/li_agentcore/src/
        exclude: ^packages/li_agentcore/tests/

  # dhagent Service hooks
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff
        name: ruff-dhagent
        args: [--fix, --exit-non-zero-on-fix]
        files: ^services/dhagent/
        exclude: ^services/dhagent/\.venv/
      - id: ruff-format
        name: ruff-format-dhagent
        files: ^services/dhagent/
        exclude: ^services/dhagent/\.venv/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.17.0
    hooks:
      - id: mypy
        name: mypy-dhagent
        additional_dependencies: [types-requests, types-PyYAML, pydantic]
        args: [--ignore-missing-imports]
        files: ^services/dhagent/src/
        exclude: ^services/dhagent/tests/

  # Local test hooks using UV with test groups
  - repo: local
    hooks:
      - id: pytest-check-li_agentcore
        name: pytest-check-li_agentcore
        entry: bash -c 'cd packages/li_agentcore && if [ -d tests ] && [ "$(find tests -name "test_*.py" | head -1)" ]; then uv run --group test pytest || [ $? -eq 5 ]; else echo "No proper test files found, skipping pytest for li_agentcore package"; fi'
        language: system
        files: ^packages/li_agentcore/
        pass_filenames: false

      - id: pytest-check-dhagent
        name: pytest-check-dhagent
        entry: bash -c 'cd services/dhagent && if [ -d tests ] && [ "$(find tests -name "test_*.py" | head -1)" ]; then uv run --group test pytest || [ $? -eq 5 ]; else echo "No proper test files found, skipping pytest for dhagent service"; fi'
        language: system
        files: ^services/dhagent/
        pass_filenames: false
