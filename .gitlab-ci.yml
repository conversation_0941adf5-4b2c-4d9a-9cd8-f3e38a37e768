# GitLab CI/CD Pipeline for DeepHeal Services & Packages
image: artifactory.ep.chehejia.com/docker-remote/python:3.12

# Cache dependencies for faster builds
cache:
  paths:
    - .cache/uv
    - packages/li_agentcore/.venv/
    - services/dhagent/.venv/

# Define stages
stages:
  - install
  - lint
  - test

variables:
  # Use pip cache directory for uv
  UV_CACHE_DIR: "$CI_PROJECT_DIR/.cache/uv"

# Install dependencies for li_agentcore package
install-li_agentcore:
  stage: install
  before_script:
    - apt-get update && apt-get install -y curl
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd packages/li_agentcore
    - uv sync --group dev --group test
  artifacts:
    paths:
      - packages/li_agentcore/.venv/
    expire_in: 1 hour

# Install dependencies for dhagent service
install-dhagent:
  stage: install
  before_script:
    - apt-get update && apt-get install -y curl
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd services/dhagent
    - uv sync --group dev --group test
  artifacts:
    paths:
      - services/dhagent/.venv/
    expire_in: 1 hour

# Lint li_agentcore package
lint-li_agentcore:
  stage: lint
  dependencies:
    - install-li_agentcore
  before_script:
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd packages/li_agentcore
    - uv run ruff check .
    - uv run ruff format --check .
    - uv run mypy src/ --ignore-missing-imports

# Lint dhagent service
lint-dhagent:
  stage: lint
  dependencies:
    - install-dhagent
  before_script:
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd services/dhagent
    - uv run ruff check .
    - uv run ruff format --check .
    - uv run mypy src/ --ignore-missing-imports

# Test li_agentcore package
test-li_agentcore:
  stage: test
  dependencies:
    - install-li_agentcore
  before_script:
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd packages/li_agentcore
    - |
      if [ -d tests ] && [ "$(find tests -name "test_*.py" | head -1)" ]; then
        uv run pytest tests/ --cov=src --cov-report=xml --cov-report=term || [ $? -eq 5 ]
      else
        echo "No proper test files found, skipping pytest for li_agentcore package"
      fi
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: packages/li_agentcore/coverage.xml
    expire_in: 1 week
  coverage: '/TOTAL.*\s+(\d+%)$/'
  allow_failure: true  # Allow failure if no tests exist

# Test dhagent service
test-dhagent:
  stage: test
  dependencies:
    - install-dhagent
  before_script:
    - curl -LsSf https://astral.sh/uv/install.sh | sh
    - export PATH="$HOME/.local/bin:$PATH"
  script:
    - cd services/dhagent
    - uv run pytest tests/ --cov=src --cov-report=xml --cov-report=term
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: services/dhagent/coverage.xml
    expire_in: 1 week
  coverage: '/TOTAL.*\s+(\d+%)$/'
