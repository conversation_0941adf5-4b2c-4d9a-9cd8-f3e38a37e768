# Li_AgentCore - Shared Entities Package


## events 模块

面向 CloudEvents 的事件模型与工具，提供事件数据结构、构建器、类型校验、HTTP 传输以及 HITL（Human-In-The-Loop）负载构建。

### 发送 AgentEvent

#### Structured HTTP AgentEvent
```python
from li_agentcore.events import AgentEvent
from li_agentcore.events import to_http_event, build_http_request
import requests

# Create a AgentEvent
agent_event = AgentEvent(
        source="agent.event.test",
        type="agent.event.test.push",
    )

headers, body = build_http_request(agent_event)
requests.post("<url>", headers=headers, data=body)
```
#### Binary HTTP AgentEvent
```python
from li_agentcore.events import AgentEvent
from li_agentcore.events import to_http_event, build_http_request
import requests

# Create a AgentEvent
agent_event = AgentEvent(
        source="agent.event.test",
        type="agent.event.test.push",
    )

headers, body = build_http_request(agent_event, mode="binary")
requests.post("<url>", headers=headers, data=body)

```

### 1) 核心模型

- AgentEvent: 基于 cloudevents.pydantic.CloudEvent 的扩展，新增 parentid/rootid 与结构化数据容器
- AgentEventData: 承载 content/metadata，content.parts 支持多种内容部件
- DataContentPart: 单个内容部件，type 取值为 text/file/data/error

示例（直接创建）
```python
from li_agentcore.events import AgentEvent

root = AgentEvent.create_event(
    source="svc.auth",
    type="agent.authorize",
    subject="user-123",
    content={"parts": {"type": "text", "text": "hello"}},
    metadata={"trace": "t1"},
)

child = AgentEvent.create_event(
    source="svc.auth",
    type="agent.invoke",
    parentid=str(root.id),
    rootid=str(root.rootid),
    content={"parts": {"type": "data", "data": {"k": 1}}},
)
```

### 2) 构建器（AgentEventBuilder）

链式 API 便捷构建事件与内容部件。
```python
from li_agentcore.events import AgentEventBuilder

ev = (
    AgentEventBuilder()
    .source("svc.payment")
    .type("agent.invoke")
    .add_text_part("pay")
    .add_data_part({"amount": 100})
    .set_metadata(correlation_id="c-1")
    .build()
)
```

### 3) 内容部件（AgentEventData / DataContentPart）

为事件添加多种内容部件，序列化时仅输出与 type 对应的字段。
```python
from li_agentcore.events import AgentEvent, DataContentPart

e = AgentEvent.create_event(source="svc", type="user.send")
e.add_text_part("hi").add_file_part({"name": "a.txt"}).add_error_part({"code": "E"})

part = DataContentPart.data_part({"k": "v"})
```

### 4) 事件类型与校验（AgentEventType/ClientType/EventTypeValidator）

- 预置类型：user.send、agent.invoke 等
- 需要 client 后缀的基类：agent.interrupt、user.resume、agent.authorize、user.grant
```python
from li_agentcore.events import AgentEventBuilder, AgentEventType
from li_agentcore.events import EventTypeValidator, ClientType

etype = EventTypeValidator.build_client_event_type(AgentEventType.AGENT_AUTHORIZE, ClientType.ROBOT)
ev = AgentEventBuilder().source("svc").type(etype).add_text_part("go").build()
```

### 5) HTTP 传输（to_http_event/build_http_request）

- to_http_event: AgentEvent → cloudevents.http.CloudEvent
- build_http_request: 生成 (headers, body)，mode 支持 structured/binary
```python
from li_agentcore.events import AgentEvent
from li_agentcore.events.http_transports import to_http_event, build_http_request

evt = AgentEvent.create_event(source="svc", type="my.custom", content={"parts": {"type": "text", "text": "hi"}})
ce = to_http_event(evt)
headers, body = build_http_request(evt, "structured")
```

### 6) 异常（exceptions）

- EventTypeError: 事件类型非法
- HierarchyError: 层级关系错误（子事件需显式 rootid）
- TransportError: 传输模式非法
- SerializationError: 编解码失败（含 format_type/operation）
- BuilderError: 构建器参数错误

### 7) HITL 负载（HumanInterruptBuilder）

构建最小且约束的人工中断负载，可序列化为 JSON 或字典，并作为 data 部件加入事件。
```python
from li_agentcore.events import AgentEventBuilder
from li_agentcore.events.human_interrupt_builder import HumanInterruptBuilder
from li_agentcore.events.http_transports import build_http_request

hitl_json = (
    HumanInterruptBuilder()
    .action("confirm")
    .config(allow_edit=False)
    .description("请确认")
    .to_json()
)

ev = (
    AgentEventBuilder()
    .source("agent.event.test")
    .type("agent.event.test.push")
    .add_data_part(hitl_json)
    .build()
)
headers, body = build_http_request(ev, "structured")
```

也可输出字典：
```python
payload = HumanInterruptBuilder().action("a").to_dict()
```
