import pytest

from packages.li_agentcore.src.li_agentcore.events import Agent<PERSON>vent


def test_create_event_minimal_and_hierarchy():
    e = AgentEvent.create_event(source="svc", type="my.custom")
    assert e.parentid == ""
    assert e.rootid == e.id


def test_child_requires_root():
    with pytest.raises(ValueError, match="Child events.*must provide explicit rootid"):
        AgentEvent.create_event(source="svc", type="my.custom", parentid="p")


def test_child_valid():
    root = AgentEvent.create_event(source="svc", type="my.custom")
    child = AgentEvent.create_event(source="svc", type="my.custom", parentid=root.id, rootid=root.id)
    assert child.parentid == root.id
    assert child.rootid == root.id


def test_direct_init_wraps_data():
    e = AgentEvent(
        source="svc",
        type="my.custom",
        data={"content": {"parts": {"type": "text", "text": "hi"}}},
    )
    assert e.data.content["parts"][0] == {"type": "text", "text": "hi"}


def test_data_convenient_construction():
    e = (
        (
            AgentEvent(
                source="svc",
                type="my.custom",
            )
            .add_data_part({"test": "test"})
            .add_file_part("test.txt")
        )
        .add_error_part("error")
        .set_metadata(x=1)
    )
    assert e.data.content["parts"][0] == {"type": "data", "data": {"test": "test"}}
    assert e.data.content["parts"][1] == {"type": "file", "file": "test.txt"}
    assert e.data.content["parts"][2] == {"type": "error", "error": "error"}
    assert e.data.metadata["x"] == 1
