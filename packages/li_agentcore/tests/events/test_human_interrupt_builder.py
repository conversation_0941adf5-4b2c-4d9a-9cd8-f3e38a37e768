import json

import pytest

from packages.li_agentcore.src.li_agentcore.events.exceptions import BuilderError
from packages.li_agentcore.src.li_agentcore.events.human_interrupt_builder import HumanInterruptBuilder


def test_build_minimal_valid():
    b = HumanInterruptBuilder().action("do.something")
    payload = b.build()
    assert payload["action_request"] == {"action": "do.something", "args": {}}
    assert payload["config"] == {
        "allow_ignore": True,
        "allow_respond": True,
        "allow_edit": True,
        "allow_accept": True,
    }
    assert "description" not in payload


def test_args_and_arg_merge():
    payload = HumanInterruptBuilder().action("run").args(x=1, y=2).arg("y", 3).build()
    assert payload["action_request"]["args"] == {"x": 1, "y": 3}


def test_config_toggles_and_bulk():
    payload = (
        HumanInterruptBuilder()
        .action("run")
        .allow_ignore(False)
        .allow_respond(False)
        .config(allow_edit=False, allow_accept=False)
        .build()
    )
    assert payload["config"] == {
        "allow_ignore": False,
        "allow_respond": False,
        "allow_edit": False,
        "allow_accept": False,
    }


def test_description_optional():
    payload = HumanInterruptBuilder().action("run").description("desc").build()
    assert payload["description"] == "desc"


def test_to_json_roundtrip_equals_build():
    b = HumanInterruptBuilder().action("run").args(k=1).allow_ignore(False)
    json_str = b.to_json(indent=2)
    parsed = json.loads(json_str)
    assert parsed == b.build()


def test_invalid_action_required():
    with pytest.raises(BuilderError, match="action is required"):
        HumanInterruptBuilder().build()


def test_invalid_action_non_empty_string():
    with pytest.raises(BuilderError, match="action is required"):
        HumanInterruptBuilder().action("   ").build()
