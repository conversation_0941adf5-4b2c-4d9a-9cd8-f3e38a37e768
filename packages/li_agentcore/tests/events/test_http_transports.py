import json

import pytest

from packages.li_agentcore.src.li_agentcore.events import AgentEvent, AgentEventBuilder, http_transports
from packages.li_agentcore.src.li_agentcore.events.exceptions import SerializationError, TransportError
from packages.li_agentcore.src.li_agentcore.events.http_transports import build_http_request, to_http_event
from packages.li_agentcore.src.li_agentcore.events.human_interrupt_builder import HumanInterruptBuilder


def _make_event():
    return AgentEvent.create_event(
        source="svc",
        type="my.custom",
        content={"parts": {"type": "text", "text": "hi"}},
    )


def test_to_http_event_data_equals_model_dump():
    e = _make_event()
    ce = to_http_event(e)
    assert isinstance(ce.data, dict)
    assert ce.data == e.data.model_dump()


def test_build_http_request_structured_mode_headers_and_body_json():
    e = _make_event()
    headers, body = build_http_request(e, "structured")
    assert isinstance(headers, dict)
    assert isinstance(body, bytes | bytearray)
    ct = next((v for k, v in headers.items() if k.lower() == "content-type"), "")
    assert "application/cloudevents+json" in ct
    parsed = json.loads(body.decode("utf-8"))
    assert parsed["data"] == e.data.model_dump()
    assert parsed["id"] == str(e.id)


def test_build_http_request_binary_mode_headers_and_body_json():
    e = _make_event()
    headers, body = build_http_request(e, "binary")
    ct = next((v for k, v in headers.items() if k.lower() == "content-type"), "")
    assert "application/json" in ct
    assert headers.get("ce-id") == str(e.id)
    assert headers.get("ce-type") == e.type
    assert headers.get("ce-source") == e.source
    parsed = json.loads(body.decode("utf-8"))
    assert parsed == e.data.model_dump()


def test_build_http_request_invalid_mode_raises_transport_error():
    e = _make_event()
    with pytest.raises(TransportError):
        build_http_request(e, "invalid")


def test_structured_serialization_error_wrapped(monkeypatch):
    e = _make_event()

    def boom(_):
        raise Exception("boom")

    monkeypatch.setattr(http_transports, "to_structured", boom)
    with pytest.raises(SerializationError) as ei:
        build_http_request(e, "structured")
    assert ei.value.format_type == "structured"
    assert ei.value.operation == "encode"


essential_binary_header_keys = ["ce-id", "ce-type", "ce-source"]


def test_binary_serialization_error_wrapped(monkeypatch):
    e = _make_event()

    def boom(_):
        raise Exception("boom")

    monkeypatch.setattr(http_transports, "to_binary", boom)
    with pytest.raises(SerializationError) as ei:
        build_http_request(e, "binary")
    assert ei.value.format_type == "binary"
    assert ei.value.operation == "encode"


def test_human_interrupt_send():
    hitl = HumanInterruptBuilder().action("test").config(allow_edit=False).description("hhhhhh").to_json()
    agent_event = (
        AgentEventBuilder().source("agent.event.test").type("agent.event.test.push").add_data_part(hitl).build()
    )
    headers, body = build_http_request(agent_event, "structured")
    assert isinstance(headers, dict)
    assert isinstance(body, bytes | bytearray)
    ct = next((v for k, v in headers.items() if k.lower() == "content-type"), "")
    assert "application/cloudevents+json" in ct
    parsed = json.loads(body.decode("utf-8"))
    assert parsed["data"] == agent_event.data.model_dump()
    parts = parsed["data"]["content"]["parts"]
    assert isinstance(parts, list)
    assert len(parts) >= 1
    assert parts[0]["type"] == "data"
    assert parts[0]["data"] == hitl
    assert parsed["type"] == "agent.event.test.push"
    assert parsed["source"] == "agent.event.test"
    assert parsed["id"] == str(agent_event.id)
