import pytest

from packages.li_agentcore.src.li_agentcore.events import AgentEventData


def test_parts_normalization_and_extras_preservation():
    d = AgentEventData(
        **{
            "content1": "x",
            "content": {"parts": {"type": "text", "text": "hello"}},
            "metadata": {"m": 1},
            "extra_k": {"a": 1},
        }
    )
    assert d.content["parts"][0] == {"type": "text", "text": "hello"}
    assert d.metadata == {"m": 1}
    assert d.content1 == "x"
    assert d.extra_k == {"a": 1}


def test_add_part_helpers():
    d = AgentEventData()
    d.add_text_part("t").add_file_part("/f").add_data_part("{}").add_error_part("e")
    parts = d.content["parts"]
    assert parts[0] == {"type": "text", "text": "t"}
    assert parts[1] == {"type": "file", "file": "/f"}
    assert parts[2] == {"type": "data", "data": "{}"}
    assert parts[3] == {"type": "error", "error": "e"}


def test_parts_dict_invalid_raises():
    with pytest.raises(ValueError, match="Field 'text' is required when type is 'text'"):
        AgentEventData(**{"content": {"parts": {"type": "text"}}})


def test_parts_list_item_shape_and_alias():
    d = AgentEventData(**{"content": {"parts": [{"type": "file", "file": "/p"}]}})
    assert d.content["parts"][0] == {"type": "file", "file": "/p"}
