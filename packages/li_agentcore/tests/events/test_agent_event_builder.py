import pytest

from packages.li_agentcore.src.li_agentcore.events import AgentEventBuilder


def test_builder_minimal_and_add_parts():
    e = (
        AgentEventBuilder()
        .source("svc")
        .type("my.custom")
        .subject("subj")
        .add_text_part("t")
        .add_data_part("{}")
        .add_file_part("f")
        .set_metadata(x=1)
        .build()
    )
    assert e.source == "svc"
    assert e.type == "my.custom"
    assert e.data.content["parts"][0]["type"] == "text"
    assert e.data.content["parts"][1]["type"] == "data"
    assert e.data.content["parts"][2]["type"] == "file"
    assert e.data.metadata["x"] == 1


def test_builder_missing_required():
    try:
        AgentEventBuilder().type("t").build()
        pytest.fail("source is required")
    except ValueError as ex:
        ex_str = str(ex)
        assert "source is required" in ex_str
    try:
        AgentEventBuilder().source("s").build()
        pytest.fail("type is required")
    except ValueError as ex:
        ex_str = str(ex)
        assert "type is required" in ex_str
