import os
from unittest.mock import AsyncMock, patch

import pytest

from packages.li_agentcore.src.li_agentcore.tools.insightflow import (
    FacetMatcher,
    FacetMatcherType,
    FacetName,
    GetTraceSpansRequest,
    LogsResponse,
    QueryLogsRequest,
    SearchSpansRequest,
    TraceResponse,
    get_trace_spans,
    query_logs,
    search_spans,
)

# # Integration tests with real API calls
# class TestRealAPIIntegration:
#     """Integration tests with real API endpoints."""
#
#     @pytest.fixture
#     def real_env_setup(self):
#         """Setup real environment variables for testing."""
#         # These should be set in your test environment
#         required_envs = {
#             "INSIGHTFLOW_BASE_URL": os.getenv(
#                 "INSIGHTFLOW_BASE_URL", "http://insightflow-querier.inner.chj.cloud"
#             ),
#             "INSIGHTFLOW_LOG_BASE_URL": os.getenv(
#                 "INSIGHTFLOW_LOG_BASE_URL", "https://li-log-adapter-dev.inner.chj.cloud"
#             ),
#             "TEST_AUTH_TOKEN": os.getenv("TEST_AUTH_TOKEN", "test"),
#             "TEST_CLUSTER": os.getenv("TEST_CLUSTER", "cnhb01"),
#             "TEST_LOGSTORE": os.getenv("TEST_LOGSTORE", "default_li-log-adapter"),
#         }
#
#         # Skip tests if required environment variables are not set
#         missing_envs = [k for k, v in required_envs.items() if not v]
#         if missing_envs:
#             pytest.skip(f"Missing required environment variables: {missing_envs}")
#
#         return required_envs
#
#     @pytest.mark.integration
#     @pytest.mark.asyncio
#     async def test_search_spans_real_api(self, real_env_setup):
#         """Test search_spans with real API call."""
#         # Use recent time range (last hour)
#         to_time = int(time.time())
#         from_time = to_time - 3600  # 1 hour ago
#
#         # Create realistic matchers
#         matchers = [
#             FacetMatcher(
#                 name=FacetName.SERVICE,
#                 values=["li-log-adapter"],  # Use a service that likely exists
#                 type=FacetMatcherType.EQUAL,
#             )
#         ]
#
#         try:
#             request = SearchSpansRequest(
#                 from_time=from_time, to_time=to_time, matchers=matchers, size=10
#             )
#             result = await search_spans(request)
#
#             # Validate response structure
#             assert isinstance(result, TraceResponse)
#             assert isinstance(result.spans, list)
#             assert isinstance(result.processes, dict)
#             assert isinstance(result.spanTypes, list)
#
#             # If spans are returned, validate their structure
#             if result.spans:
#                 span = result.spans[0]
#                 assert isinstance(span, Span)
#                 assert span.traceID is not None
#                 assert span.serviceName is not None
#
#             print(f"✓ search_spans returned {len(result.spans)} spans")
#
#         except Exception as e:
#             pytest.fail(f"Real API call failed: {e}")
#
#     @pytest.mark.integration
#     @pytest.mark.asyncio
#     async def test_get_trace_spans_real_api(self, real_env_setup):
#         """Test get_trace_spans with real API call."""
#         # First, get a trace ID from search_spans
#         to_time = int(time.time())
#         from_time = to_time - 7200  # 2 hours ago for better chance of finding spans
#
#         # Search for any spans first
#         search_request = SearchSpansRequest(
#             from_time=from_time, to_time=to_time, size=1
#         )
#         search_result = await search_spans(search_request)
#
#         if not search_result.spans:
#             pytest.skip("No spans found in recent time range for trace lookup test")
#
#         # Use the first span's trace ID
#         trace_id = search_result.spans[0].traceID
#
#         try:
#             request = GetTraceSpansRequest(
#                 trace_id=trace_id, from_time=from_time, to_time=to_time
#             )
#             result = await get_trace_spans(request)
#
#             # Validate response structure
#             assert isinstance(result, TraceResponse)
#             assert isinstance(result.spans, list)
#             assert isinstance(result.processes, dict)
#             assert result.traceID == trace_id
#
#             # Should have at least one span for the given trace
#             assert len(result.spans) > 0
#
#             # All spans should have the same trace ID
#             for span in result.spans:
#                 assert span.traceID == trace_id
#
#             print(
#                 f"✓ get_trace_spans returned {len(result.spans)} spans for trace {trace_id}"
#             )
#
#         except Exception as e:
#             pytest.fail(f"Real API call failed: {e}")
#
#     @pytest.mark.integration
#     @pytest.mark.asyncio
#     async def test_query_logs_real_api(self, real_env_setup):
#         """Test query_logs with real API call."""
#         auth_token = real_env_setup["TEST_AUTH_TOKEN"]
#         cluster = real_env_setup["TEST_CLUSTER"]
#         logstore = real_env_setup["TEST_LOGSTORE"]
#
#         # Use recent time range
#         to_time = int(time.time())
#         from_time = to_time - 1800  # 30 minutes ago
#
#         try:
#             request = QueryLogsRequest(
#                 auth_token=auth_token,
#                 cluster=cluster,
#                 logstore=logstore,
#                 query="*",
#                 from_time=from_time,
#                 to_time=to_time,
#             )
#             result = await query_logs(request)
#
#             # Validate response structure
#             assert isinstance(result, LogsResponse)
#             assert isinstance(result.result, list)
#
#             # If logs are returned, validate their structure
#             if result.result:
#                 log_record = result.result[0]
#                 assert isinstance(log_record, LogRecord)
#                 # At least one of the standard fields should be present
#                 assert any(
#                     [
#                         log_record.container_ip,
#                         log_record.container_name,
#                         log_record.namespace_p,
#                         log_record.source,
#                         log_record.time,
#                     ]
#                 )
#
#             print(f"✓ query_logs returned {len(result.result)} log entries")
#
#         except Exception as e:
#             pytest.fail(f"Real API call failed: {e}")
#
#     @pytest.mark.integration
#     @pytest.mark.asyncio
#     async def test_error_handling_real_api(self, real_env_setup):
#         """Test error handling with real API calls."""
#
#         # Test invalid time range
#         with pytest.raises(ValueError, match="Start time must be less than end time"):
#             request = SearchSpansRequest(
#                 from_time=int(time.time()), to_time=int(time.time()) - 3600
#             )
#             await search_spans(request)
#
#         # Test invalid trace ID
#         try:
#             request = GetTraceSpansRequest(
#                 trace_id="invalid-trace-id-12345",
#                 from_time=int(time.time()) - 3600,
#                 to_time=int(time.time()),
#             )
#             result = await get_trace_spans(request)
#             # Should return empty spans list for invalid trace ID
#             assert len(result.spans) == 0
#         except Exception:
#             # Some APIs might return 404 or other errors for invalid trace ID
#             pass
#
#         # Test logs with invalid auth token
#         with pytest.raises(Exception):  # Could be ClientError or other auth error
#             request = QueryLogsRequest(
#                 auth_token="invalid-token",
#                 cluster=real_env_setup["TEST_CLUSTER"],
#                 logstore=real_env_setup["TEST_LOGSTORE"],
#                 query="*",
#                 from_time=int(time.time()) - 3600,
#                 to_time=int(time.time()),
#             )
#             await query_logs(request)


# Mock-based unit tests (existing tests)
class TestSearchSpans:
    """Test search_spans function"""

    @pytest.fixture
    def mock_env(self):
        """Mock environment variables"""
        with patch.dict(
            os.environ,
            {"INSIGHTFLOW_BASE_URL": "http://insightflow-querier.inner.chj.cloud"},
        ):
            yield

    @pytest.fixture
    def sample_matchers(self):
        """Sample matchers for testing"""
        return [
            FacetMatcher(
                name=FacetName.SERVICE,
                values=["test-service"],
                type=FacetMatcherType.EQUAL,
            ),
            FacetMatcher(name=FacetName.STATUS, values=["Error"], type=FacetMatcherType.EQUAL),
        ]

    @pytest.fixture
    def sample_api_response(self):
        """Sample API response data"""
        return {
            "data": {
                "spans": [
                    {
                        "traceID": "test-trace-id",
                        "spanID": "test-span-id",
                        "serviceName": "test-service",
                        "operationName": "test-operation",
                        "spanType": "http.client",
                        "statusCode": "Error",
                        "startTime": 1754376433798172,
                        "duration": 4726,
                        "processID": "p1",
                    }
                ],
                "processes": {"p1": {"serviceName": "test-service"}},
                "spanTypes": ["http", "db"],
            }
        }

    @pytest.mark.asyncio
    async def test_search_spans_success(self, mock_env, sample_matchers, sample_api_response):
        """Test successful span search"""
        mock_response = AsyncMock()
        mock_response.json.return_value = sample_api_response
        mock_response.raise_for_status.return_value = None

        with patch("aiohttp.ClientSession.post") as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response

            result = await search_spans(
                SearchSpansRequest(
                    from_time=1754362621,
                    to_time=1754365321,
                    matchers=sample_matchers,
                    size=100,
                )
            )

            assert isinstance(result, TraceResponse)
            assert len(result.spans) == 1
            assert result.spans[0].traceID == "test-trace-id"
            assert result.spans[0].serviceName == "test-service"
            assert len(result.processes) == 1
            assert "p1" in result.processes

    @pytest.mark.asyncio
    async def test_search_spans_invalid_time_range(self, mock_env):
        """Test search_spans with invalid time range"""
        with pytest.raises(ValueError, match="Start time must be less than end time"):
            await search_spans(SearchSpansRequest(from_time=1754365321, to_time=1754362621))

    @pytest.mark.asyncio
    async def test_search_spans_missing_data_field(self, mock_env):
        """Test search_spans with invalid API response"""
        mock_response = AsyncMock()
        mock_response.json.return_value = {"invalid": "response"}
        mock_response.raise_for_status.return_value = None

        with patch("aiohttp.ClientSession.post") as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response

            with pytest.raises(ValueError, match="API response format error: missing data field"):
                await search_spans(SearchSpansRequest(from_time=1754362621, to_time=1754365321))


class TestGetTraceSpans:
    """Test get_trace_spans function"""

    @pytest.fixture
    def mock_env(self):
        """Mock environment variables"""
        with patch.dict(
            os.environ,
            {"INSIGHTFLOW_BASE_URL": "http://insightflow-querier.inner.chj.cloud"},
        ):
            yield

    @pytest.fixture
    def sample_trace_response(self):
        """Sample trace API response data"""
        return {
            "data": {
                "traceID": "test-trace-id",
                "spans": [
                    {
                        "traceID": "test-trace-id",
                        "spanID": "span-1",
                        "serviceName": "service-a",
                        "operationName": "operation-1",
                        "statusCode": "Ok",
                        "startTime": 1754376240000000,
                        "duration": 331500171,
                        "processID": "p1",
                        "references": [
                            {
                                "refType": "CHILD_OF",
                                "traceID": "test-trace-id",
                                "spanID": "parent-span-id",
                            }
                        ],
                    }
                ],
                "processes": {"p1": {"serviceName": "service-a"}},
                "spanTypes": ["http", "internal"],
            }
        }

    @pytest.mark.asyncio
    async def test_get_trace_spans_success(self, mock_env, sample_trace_response):
        """Test successful trace span retrieval"""
        mock_response = AsyncMock()
        mock_response.json.return_value = sample_trace_response
        mock_response.raise_for_status.return_value = None

        with patch("aiohttp.ClientSession.get") as mock_get:
            mock_get.return_value.__aenter__.return_value = mock_response

            result = await get_trace_spans(
                GetTraceSpansRequest(trace_id="test-trace-id", from_time=1754362621, to_time=1754365321)
            )

            assert isinstance(result, TraceResponse)
            assert result.traceID == "test-trace-id"
            assert len(result.spans) == 1
            assert result.spans[0].spanID == "span-1"
            assert len(result.spans[0].references) == 1

    @pytest.mark.asyncio
    async def test_get_trace_spans_empty_trace_id(self, mock_env):
        """Test get_trace_spans with empty trace ID"""
        with pytest.raises(ValueError, match="trace_id cannot be empty"):
            await get_trace_spans(GetTraceSpansRequest(trace_id="", from_time=1754362621, to_time=1754365321))


class TestQueryLogs:
    """Test query_logs function"""

    @pytest.fixture
    def mock_env(self):
        """Mock environment variables"""
        with patch.dict(
            os.environ,
            {
                "INSIGHTFLOW_LOG_BASE_URL": "https://li-log-adapter-dev.inner.chj.cloud",
                "INSIGHTFLOW_LOG_AUTH_TOKEN": "xxxxx",
            },
        ):
            yield

    @pytest.fixture
    def sample_log_response(self):
        """Sample log API response data"""
        return {
            "data": {
                "result": [
                    {
                        "_container_ip": "************",
                        "_container_name": "test-service",
                        "_namespace_p": "prod",
                        "_source_": "stdout",
                        "_time_": "2025-08-08T06:15:57.956899486Z",
                        "level": "ERROR",
                        "message": "Test error message",
                        "custom_field": "custom_value",
                    }
                ]
            }
        }

    @pytest.mark.asyncio
    async def test_query_logs_success(self, mock_env, sample_log_response):
        """Test successful log query"""
        mock_response = AsyncMock()
        mock_response.json.return_value = sample_log_response
        mock_response.raise_for_status.return_value = None

        with patch("aiohttp.ClientSession.post") as mock_post:
            mock_post.return_value.__aenter__.return_value = mock_response

            result = await query_logs(
                QueryLogsRequest(
                    auth_token="Bearer test-token",
                    cluster="test-cluster",
                    logstore="test-logstore",
                    query="*",
                    from_time=1754362621,
                    to_time=1754365321,
                )
            )

            assert isinstance(result, LogsResponse)
            assert len(result.result) == 1
            log_record = result.result[0]
            assert log_record.container_ip == "************"
            assert log_record.container_name == "test-service"
            assert log_record.extra_fields is not None
            assert "level" in log_record.extra_fields
            assert log_record.extra_fields["level"] == "ERROR"

    @pytest.mark.asyncio
    async def test_query_logs_missing_cluster(self, mock_env):
        """Test query_logs with missing cluster"""
        with pytest.raises(ValueError, match="cluster cannot be empty"):
            await query_logs(
                QueryLogsRequest(
                    auth_token="Bearer test-token",
                    cluster="",
                    logstore="test-logstore",
                    query="*",
                    from_time=1754362621,
                    to_time=1754365321,
                )
            )

    @pytest.mark.asyncio
    async def test_query_logs_missing_logstore(self, mock_env):
        """Test query_logs with missing logstore"""
        with pytest.raises(ValueError, match="logstore cannot be empty"):
            await query_logs(
                QueryLogsRequest(
                    auth_token="Bearer test-token",
                    cluster="test-cluster",
                    logstore="",
                    query="*",
                    from_time=1754362621,
                    to_time=1754365321,
                )
            )


class TestDataModels:
    """Test data model classes"""

    def test_facet_matcher_creation(self):
        """Test FacetMatcher creation"""
        matcher = FacetMatcher(name=FacetName.SERVICE, values=["test-service"], type=FacetMatcherType.EQUAL)
        assert matcher.name == FacetName.SERVICE
        assert matcher.values == ["test-service"]
        assert matcher.type == FacetMatcherType.EQUAL

    def test_facet_name_enum_values(self):
        """Test FacetName enum values"""
        assert FacetName.SERVICE == "service"
        assert FacetName.HTTP_METHOD == "http.method"
        assert FacetName.DB_NAME == "db.name"

    def test_facet_matcher_type_enum_values(self):
        """Test FacetMatcherType enum values"""
        assert FacetMatcherType.EQUAL == 1
        assert FacetMatcherType.NOT_EQUAL == 2
        assert FacetMatcherType.LIKE == 3
        assert FacetMatcherType.GREATER_THAN == 7


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
