import json
from typing import Any, TypedDict

from .exceptions import BuilderError


class ActionRequest(TypedDict):
    action: str
    args: dict[str, Any]


class HumanInterruptConfig(TypedDict):
    allow_ignore: bool
    allow_respond: bool
    allow_edit: bool
    allow_accept: bool


class HumanInterrupt(TypedDict, total=False):
    action_request: ActionRequest
    config: HumanInterruptConfig
    description: str


class HumanInterruptBuilder:
    """Builder for creating HumanInterrupt with fluent API.

    Provides a minimal, typed shape for HITL prompts. Use allow_* toggles
    to control user capabilities and build() to emit a TypedDict payload.
    """

    def __init__(self) -> None:
        self._action: str | None = None
        self._args: dict[str, Any] = {}
        self._description: str | None = None
        self._allow_ignore: bool = True
        self._allow_respond: bool = True
        self._allow_edit: bool = True
        self._allow_accept: bool = True

    def action(self, action: str) -> "HumanInterruptBuilder":
        """设置动作"""
        self._action = action
        return self

    def args(self, **kwargs) -> "HumanInterruptBuilder":  # type: ignore[no-untyped-def]
        """设置参数"""
        self._args.update(kwargs)
        return self

    def arg(self, key: str, value: Any) -> "HumanInterruptBuilder":
        """设置单个参数"""
        self._args[key] = value
        return self

    def description(self, description: str) -> "HumanInterruptBuilder":
        """设置描述"""
        self._description = description
        return self

    def allow_ignore(self, allow: bool = True) -> "HumanInterruptBuilder":
        """设置是否允许忽略"""
        self._allow_ignore = allow
        return self

    def allow_respond(self, allow: bool = True) -> "HumanInterruptBuilder":
        """设置是否允许响应"""
        self._allow_respond = allow
        return self

    def allow_edit(self, allow: bool = True) -> "HumanInterruptBuilder":
        """设置是否允许编辑"""
        self._allow_edit = allow
        return self

    def allow_accept(self, allow: bool = True) -> "HumanInterruptBuilder":
        """设置是否允许接受"""
        self._allow_accept = allow
        return self

    def config(
        self,
        *,
        allow_ignore: bool | None = None,
        allow_respond: bool | None = None,
        allow_edit: bool | None = None,
        allow_accept: bool | None = None,
    ) -> "HumanInterruptBuilder":
        """批量设置配置"""
        if allow_ignore is not None:
            self._allow_ignore = allow_ignore
        if allow_respond is not None:
            self._allow_respond = allow_respond
        if allow_edit is not None:
            self._allow_edit = allow_edit
        if allow_accept is not None:
            self._allow_accept = allow_accept
        return self

    def build(self) -> HumanInterrupt:
        """Build a HumanInterrupt payload with minimal validation."""
        if not self._action or not isinstance(self._action, str) or not self._action.strip():
            raise BuilderError("action is required and must be a non-empty string")
        if not isinstance(self._args, dict):
            raise BuilderError("args must be a dictionary")
        for k in (self._allow_ignore, self._allow_respond, self._allow_edit, self._allow_accept):
            if not isinstance(k, bool):
                raise BuilderError("capability flags must be boolean values")

        # 构建 ActionRequest
        action_request: ActionRequest = {"action": self._action, "args": self._args}

        # 构建 HumanInterruptConfig
        config: HumanInterruptConfig = {
            "allow_ignore": self._allow_ignore,
            "allow_respond": self._allow_respond,
            "allow_edit": self._allow_edit,
            "allow_accept": self._allow_accept,
        }

        # 构建 HumanInterrupt
        result: HumanInterrupt = {"action_request": action_request, "config": config}

        if self._description:
            result["description"] = self._description

        return result

    def to_json(self, ensure_ascii: bool = False, indent: int | None = None) -> str:
        """Build and return the HumanInterrupt payload as a JSON string."""
        payload = self.build()
        return json.dumps(payload, ensure_ascii=ensure_ascii, indent=indent)

    def to_dict(self) -> dict[str, Any]:
        """Build and return the HumanInterrupt payload as a Python dict."""
        return dict(self.build())
