"""
HTTP transport utilities for AgentEvent.

Converts AgentEvent <-> cloudevents.http.CloudEvent and builds/parses HTTP envelopes.
- to_http_event: AgentEvent -> CloudEvent with JSON data
- build_http_request: returns (headers, body) in 'structured' or 'binary' mode
- parse_http_request: reconstructs AgentEvent from headers/body (structured or binary)

Raises SerializationError/TransportError on invalid mode or encode/decode failures.
"""

from __future__ import annotations

from typing import TYPE_CHECKING, cast

from cloudevents.http import (
    CloudEvent as HttpCloudEvent,
    to_binary,
    to_structured,
)

if TYPE_CHECKING:
    from .agent_event import AgentEvent
from .agent_event_data import AgentEventData
from .exceptions import SerializationError, TransportError


def to_http_event(event: AgentEvent) -> HttpCloudEvent:
    attrs = event.model_dump(exclude={"data"})
    time_val = getattr(event, "time", None)
    if time_val is not None:
        attrs["time"] = time_val.isoformat()
    if getattr(event, "id", None) is not None:
        attrs["id"] = str(event.id)
    if "datacontenttype" not in attrs or not attrs.get("datacontenttype"):
        attrs["datacontenttype"] = "application/json"
    data_obj = getattr(event, "data", None)
    data = data_obj.model_dump() if isinstance(data_obj, AgentEventData) else None
    return HttpCloudEvent(attributes=attrs, data=data)


def build_http_request(event: AgentEvent, mode: str = "structured") -> tuple[dict[str, str], bytes]:
    http_evt = to_http_event(event)
    if mode == "structured":
        try:
            result = to_structured(http_evt)
            return cast("tuple[dict[str, str], bytes]", result)
        except Exception as exc:
            raise SerializationError(str(exc), format_type="structured", operation="encode") from exc
    if mode == "binary":
        try:
            result = to_binary(http_evt)
            return cast("tuple[dict[str, str], bytes]", result)
        except Exception as exc:
            raise SerializationError(str(exc), format_type="binary", operation="encode") from exc
    raise TransportError("mode must be 'structured' or 'binary'", mode=mode)
