"""
Builder pattern for fluent Agent<PERSON>vent creation
"""

from __future__ import annotations

from typing import Any

from .agent_event import Agent<PERSON><PERSON>
from .agent_event_data import Agent<PERSON>ventData, DataContentPart


class AgentEventBuilder:
    """
    Builder for creating AgentEvent instances with fluent API
    """

    _source: str | None
    _type: str | None
    _subject: str | None
    _parent_id: str
    _root_id: str | None
    _event_data: AgentEventData
    _extra_attributes: dict[str, Any]

    def __init__(self) -> None:
        self._source = None
        self._type = None
        self._subject = None
        self._parent_id = ""
        self._root_id = None
        self._event_data = AgentEventData()
        self._extra_attributes = {}

    def source(self, source: str) -> AgentEventBuilder:
        """Set the event source"""
        self._source = source
        return self

    def type(self, event_type: str) -> AgentEventBuilder:
        """Set the event type"""
        self._type = event_type
        return self

    def subject(self, subject: str) -> AgentEventBuilder:
        """Set the event subject"""
        self._subject = subject
        return self

    def parentid(self, parentid: str) -> AgentEventBuilder:
        """Set the parent event ID"""
        self._parent_id = parentid
        return self

    def rootid(self, rootid: str) -> AgentEventBuilder:
        """Set the root event ID"""
        self._root_id = rootid
        return self

    def add_text_part(self, text: str) -> AgentEventBuilder:
        """Add a text content part"""
        self._event_data.add_text_part(text)
        return self

    def add_file_part(self, file: str | dict) -> AgentEventBuilder:
        """Add a file content part"""
        self._event_data.add_file_part(file)
        return self

    def add_data_part(self, data: str | dict) -> AgentEventBuilder:
        """Add a data content part"""
        self._event_data.add_data_part(data)
        return self

    def add_error_part(self, error: str | dict) -> AgentEventBuilder:
        """Add an error content part"""
        self._event_data.add_error_part(error)
        return self

    def add_content_part(self, part: DataContentPart) -> AgentEventBuilder:
        """Add a custom content part"""
        self._event_data.add_part(part)
        return self

    def set_metadata(self, **metadata) -> AgentEventBuilder:  # type: ignore[no-untyped-def]
        """Set metadata fields"""
        self._event_data.set_metadata(**metadata)
        return self

    def add_content(self, **content) -> AgentEventBuilder:  # type: ignore[no-untyped-def]
        """Add custom content fields (beyond parts)"""
        self._event_data.content.update(content)
        return self

    def add_attribute(self, key: str, value: Any) -> AgentEventBuilder:
        """Add custom CloudEvent attribute"""
        self._extra_attributes[key] = value
        return self

    def build(self) -> AgentEvent:
        """Build the AgentEvent instance"""

        # Minimal pre-flight validation
        if not self._source:
            raise ValueError("source is required")
        if not self._type:
            raise ValueError("type is required")

        # Build event parameters - let AgentEvent handle defaults
        event_params = {
            "parentid": self._parent_id,
            "data": self._event_data,
            **self._extra_attributes,
        }

        event_params["source"] = self._source
        event_params["type"] = self._type
        if self._subject:
            event_params["subject"] = self._subject
        if self._root_id:
            event_params["rootid"] = self._root_id

        return AgentEvent(**event_params)

    def reset(self) -> AgentEventBuilder:
        """Reset the builder to initial state"""
        self._source = None
        self._type = None
        self._subject = None
        self._parent_id = ""
        self._root_id = None
        self._event_data = AgentEventData()
        self._extra_attributes = {}
        return self
