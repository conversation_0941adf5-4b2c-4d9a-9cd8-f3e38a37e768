"""
Event data models for structured content parts
"""

import builtins
from typing import Any, Literal

from pydantic import BaseModel, ConfigDict, Field, model_validator


class DataContentPart(BaseModel):
    """
    Base content part with type-specific fields.

    This model ensures that only the field matching the specified type is included
    in the serialized output, providing clean and minimal JSON representations.

    Example:
        text_part = DataContentPart.text_part("Hello World")
        # Output: {"type": "text", "text": "Hello World"}

        file_part = DataContentPart.file_part("/path/to/file.txt")
        # Output: {"type": "file", "file": "/path/to/file.txt"}
    """

    model_config = ConfigDict(
        # Exclude None values by default, but we'll override with custom logic
        validate_default=True,
        arbitrary_types_allowed=False,
    )

    type: Literal["text", "file", "data", "error"]

    # Type-specific fields
    text: str | None = None
    file: str | dict | None = None
    data: str | dict | None = None
    error: str | dict | None = None

    @model_validator(mode="before")
    @classmethod
    def validate_type_specific_fields(cls, values: dict) -> dict:
        """
        Validates that the field matching the type is provided.

        Other type-specific fields may be present but are ignored during serialization.

        Args:
            values: Input values dictionary

        Returns:
            Validated values dictionary

        Raises:
            ValueError: If the required field for the given type is missing
        """
        if not isinstance(values, dict):
            return values

        part_type = values.get("type")
        type_fields = {
            "text": values.get("text"),
            "file": values.get("file"),
            "data": values.get("data"),
            "error": values.get("error"),
        }

        # Check that the field matching the type is provided
        if part_type is None:
            raise ValueError("'part_type' cannot be None")

        if type_fields.get(part_type) is None:
            raise ValueError(f"Field '{part_type}' is required when type is '{part_type}'")

        # Check that other fields are None or not provided
        for field_name, field_value in type_fields.items():
            if field_name != part_type and field_value is not None:
                raise ValueError(f"Field '{field_name}' should not be provided when type is '{part_type}'")

        return values

    def model_dump(self, **kwargs) -> dict[str, Any]:  # type: ignore[no-untyped-def]
        """
        Custom serialization that excludes None fields for non-matching types.

        This method ensures that only the type field and the field matching the type
        are included in the output, providing clean and minimal representations.

        Args:
            **kwargs: Additional arguments passed to parent model_dump

        Returns:
            Dictionary containing only type and the matching type-specific field
        """
        # Get the base model dump
        data = super().model_dump(**kwargs)

        # Create a clean result with only type and the matching field
        result = {"type": data["type"]}

        # Add only the field that matches the type
        type_field_map = {
            "text": "text",
            "file": "file",
            "data": "data",
            "error": "error",
        }

        if data["type"] in type_field_map:
            field_name = type_field_map[data["type"]]
            if field_name in data and data[field_name] is not None:
                result[field_name] = data[field_name]

        return result

    def dict(self, **kwargs) -> dict[str, Any]:  # type: ignore[no-untyped-def]
        """
        Legacy dict method for backward compatibility.

        Deprecated: Use model_dump() instead.
        """
        return self.model_dump(**kwargs)

    @classmethod
    def text_part(cls, text: str) -> "DataContentPart":
        """
        Create a text content part.

        Args:
            text: The text content to include

        Returns:
            DataContentPart with type 'text' and the provided text
        """
        return cls(type="text", text=text)

    @classmethod
    def file_part(cls, file: str | builtins.dict) -> "DataContentPart":
        """
        Create a file content part.

        Args:
            file: The file path or identifier

        Returns:
            DataContentPart with type 'file' and the provided file path
        """
        return cls(type="file", file=file)

    @classmethod
    def data_part(cls, data: str | builtins.dict) -> "DataContentPart":
        """
        Create a data content part.

        Args:
            data: The data content (typically JSON string)

        Returns:
            DataContentPart with type 'data' and the provided data
        """
        return cls(type="data", data=data)

    @classmethod
    def error_part(cls, error: str | builtins.dict) -> "DataContentPart":
        """
        Create an error content part.

        Args:
            error: The error message or description

        Returns:
            DataContentPart with type 'error' and the provided error message
        """
        return cls(type="error", error=error)


class AgentEventData(BaseModel):
    """Structured event data with content parts and metadata"""

    model_config = ConfigDict(
        # Allow arbitrary types for CloudEvent compatibility
        arbitrary_types_allowed=True,
        # Use JSON-compatible serialization
        validate_default=True,
        # Preserve extra keys under data alongside content/metadata
        extra="allow",
    )

    content: dict[str, Any] = Field(default_factory=dict)
    metadata: dict[str, Any] = Field(default_factory=dict)

    @model_validator(mode="before")
    @classmethod
    def ensure_content_has_parts(cls, values: Any) -> Any:
        """Ensure content has parts field, preserve extra keys under data.
        - Always ensure content exists with parts array
        - If user provided non-list parts, coerce to list with cleaned DataContentPart when possible
        - Extra keys in 'values' remain alongside content/metadata
        """
        if not isinstance(values, dict):
            return values

        content = values.get("content")
        if not isinstance(content, dict):
            content = {}
        parts = content.get("parts")
        if isinstance(parts, list):
            normalized = []
            for idx, p in enumerate(parts):
                if not isinstance(p, dict):
                    raise ValueError(f"content.parts[{idx}] must be an object")
                normalized.append(DataContentPart(**p).model_dump())
            content["parts"] = normalized if normalized else [{}]
        elif isinstance(parts, dict):
            # Normalize single dict into a valid DataContentPart or raise
            content["parts"] = [DataContentPart(**parts).model_dump()]
        else:
            # No parts provided -> keep placeholder
            content["parts"] = [{}]
        values["content"] = content
        if "metadata" not in values or not isinstance(values.get("metadata"), dict):
            values["metadata"] = {}
        return values

    def add_part(self, part: DataContentPart) -> "AgentEventData":
        """Add a content part to the event data."""
        if "parts" not in self.content or not isinstance(self.content["parts"], list):
            self.content["parts"] = []
        if len(self.content["parts"]) == 1 and self.content["parts"][0] == {}:
            self.content["parts"] = []
        self.content["parts"].append(part.model_dump())
        return self

    def add_text_part(self, text: str) -> "AgentEventData":
        """
        Convenience method to add a text part.

        Args:
            text: Text content to add

        Returns:
            Self for method chaining
        """
        return self.add_part(DataContentPart.text_part(text))

    def add_file_part(self, file: str | dict) -> "AgentEventData":
        """
        Convenience method to add a file part.

        Args:
            file: File path or identifier to add

        Returns:
            Self for method chaining
        """
        return self.add_part(DataContentPart.file_part(file))

    def add_data_part(self, data: str | dict) -> "AgentEventData":
        """
        Convenience method to add a data part.

        Args:
            data: Data content (typically JSON string) to add

        Returns:
            Self for method chaining
        """
        return self.add_part(DataContentPart.data_part(data))

    def add_error_part(self, error: str | dict) -> "AgentEventData":
        """
        Convenience method to add an error part.

        Args:
            error: Error message or description to add

        Returns:
            Self for method chaining
        """
        return self.add_part(DataContentPart.error_part(error))

    def set_metadata(self, **kwargs) -> "AgentEventData":  # type: ignore[no-untyped-def]
        """
        Set metadata fields using keyword arguments.

        This method allows flexible metadata assignment and supports
        method chaining for fluent API usage.

        Args:
            **kwargs: Key-value pairs to add to metadata

        Returns:
            Self for method chaining
        """
        self.metadata.update(kwargs)
        return self
