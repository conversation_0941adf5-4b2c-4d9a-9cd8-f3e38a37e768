from typing import Any


class AgentEventError(Exception):
    def __init__(self, message: str, **kwargs: Any):
        super().__init__(message)
        self.message = message
        self.details = kwargs


class SchemaValidationError(AgentEventError, ValueError):
    def __init__(self, message: str, field_name: str | None = None, field_value: Any = None, **kwargs: Any):
        super().__init__(message, field_name=field_name, field_value=field_value, **kwargs)
        self.field_name = field_name
        self.field_value = field_value


class HierarchyError(AgentEventError, ValueError):
    def __init__(self, message: str, parentid: str | None = None, rootid: str | None = None, **kwargs: Any):
        super().__init__(message, parentid=parentid, rootid=rootid, **kwargs)
        self.parentid = parentid
        self.rootid = rootid


class EventTypeError(AgentEventError, ValueError):
    pass


class TransportError(AgentEventError, ValueError):
    def __init__(self, message: str, mode: str | None = None, **kwargs: Any):
        super().__init__(message, mode=mode, **kwargs)
        self.mode = mode


class SerializationError(AgentEventError, ValueError):
    def __init__(self, message: str, format_type: str | None = None, operation: str | None = None, **kwargs: Any):
        super().__init__(message, format_type=format_type, operation=operation, **kwargs)
        self.format_type = format_type
        self.operation = operation


class BuilderError(AgentEventError, ValueError):
    def __init__(self, message: str, missing_fields: list | None = None, **kwargs: Any):
        super().__init__(message, missing_fields=missing_fields, **kwargs)
        self.missing_fields = missing_fields or []
