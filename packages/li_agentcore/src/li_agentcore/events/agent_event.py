"""
AgentEvent class extending CloudEvent with agent-specific functionality
"""

import uuid
from typing import Any, cast

from cloudevents.pydantic import CloudEvent
from pydantic import Field, model_validator

from .agent_event_data import AgentEventData
from .exceptions import EventTypeError, HierarchyError
from .fields_docs import FIELD_DESCRIPTIONS
from .validators import EventTypeValidator


class AgentEvent(CloudEvent):
    """
    AgentEvent extends CloudEvent with agent-specific fields and functionality.

    Adds required parentid and rootid fields for event hierarchy tracking,
    and structured data handling with content parts and metadata.

    Validation notes:
    - Event 'type' is validated via EventTypeValidator according to reserved/client rules
    - Root events auto-derive rootid from id; child events must provide rootid explicitly
    """

    parentid: str = Field(
        title=FIELD_DESCRIPTIONS["parentid"].get("title"),
        description=FIELD_DESCRIPTIONS["parentid"].get("description"),
        default="",
    )
    rootid: str = Field(
        title=FIELD_DESCRIPTIONS["rootid"].get("title"),
        description=FIELD_DESCRIPTIONS["rootid"].get("description"),
        default="",
    )

    def __init__(self, **kwargs):  # type: ignore[no-untyped-def]
        """Initialize AgentEvent with minimal wrapping around CloudEvent
        - Do not inject defaults for CloudEvent fields
        - Only ensure 'data' is wrapped as AgentEventData
        """

        # Always ensure AgentEventData structure for consistency
        data_input = kwargs.get("data")
        if isinstance(data_input, AgentEventData):
            kwargs["data"] = data_input
        elif isinstance(data_input, dict):
            kwargs["data"] = AgentEventData(**data_input)
        else:
            # Create empty AgentEventData for any other type (including None)
            kwargs["data"] = AgentEventData()

        super().__init__(**kwargs)

    @model_validator(mode="after")
    def _post_validate(self):  # type: ignore[no-untyped-def]
        """Post-init validation leveraging CloudEvent-initialized fields.
        - Enforce type rules
        - Set rootid for root events after id is available
        - Require explicit rootid for child events
        """
        # Validate event type per requirements (allows custom types)
        if getattr(self, "type", None):
            try:
                EventTypeValidator.validate_event_type(self.type)
            except Exception as exc:
                raise EventTypeError(str(exc)) from exc

        # Handle hierarchy after CloudEvent has potentially set 'id'
        if not getattr(self, "rootid", None):
            if not getattr(self, "parentid", None):
                # Root event: ensure id exists, then mirror to rootid
                if not getattr(self, "id", None):
                    self.id = str(uuid.uuid4())
                self.rootid = self.id
            else:
                # Child event must provide explicit rootid
                raise HierarchyError(
                    "Child events (parentid set) must provide explicit rootid;"
                    "rootid should be the ID of the root event in the chain.",
                    parent_id=self.parentid,
                    root_id=self.rootid,
                )
        return self

    @classmethod
    def create_event(  # type: ignore[no-untyped-def]
        cls,
        source: str | None = None,
        type: str | None = None,
        subject: str | None = None,
        parentid: str = "",
        rootid: str | None = None,
        content: dict[str, Any] | None = None,
        metadata: dict[str, Any] | None = None,
        **kwargs,
    ) -> "AgentEvent":
        """
        Create an AgentEvent with structured data

        Args:
            source: Event source identifier
            type: Event type
            subject: Event subject
            parentid: Parent event ID (empty string for root events)
            rootid: Root event ID (if omitted for root events, will be set to id in validator)
            content: Content data for event.data.content
            metadata: Metadata for event.data.metadata
            **kwargs: Additional CloudEvent attributes

        Returns:
            AgentEvent instance
        """

        # Prepare event data only; do not set other defaults
        event_data = AgentEventData()
        if content:
            event_data.content.update(content)
        if metadata:
            event_data.metadata.update(metadata)
        if "parts" not in event_data.content:
            event_data.content["parts"] = [{}]

        event_params = {"parentid": parentid, "data": event_data, **kwargs}
        if source is not None:
            event_params["source"] = source
        if type is not None:
            event_params["type"] = type
        if subject is not None:
            event_params["subject"] = subject
        if rootid is not None:
            event_params["rootid"] = rootid

        return cls(**event_params)

    def add_text_part(self, text: str) -> "AgentEvent":
        data = cast("AgentEventData", self.data)
        data.add_text_part(text)
        return self

    def add_file_part(self, file: str | dict) -> "AgentEvent":
        data = cast("AgentEventData", self.data)
        data.add_file_part(file)
        return self

    def add_data_part(self, data: str | dict) -> "AgentEvent":
        d = cast("AgentEventData", self.data)
        d.add_data_part(data)
        return self

    def add_error_part(self, error: str | dict) -> "AgentEvent":
        data = cast("AgentEventData", self.data)
        data.add_error_part(error)
        return self

    def set_metadata(self, **kwargs) -> "AgentEvent":  # type: ignore[no-untyped-def]
        data = cast("AgentEventData", self.data)
        data.set_metadata(**kwargs)
        return self
