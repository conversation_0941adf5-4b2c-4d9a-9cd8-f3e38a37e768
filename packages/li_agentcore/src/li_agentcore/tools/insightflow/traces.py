"""InsightFlow API client for trace queries."""

import os
from enum import Enum, IntEnum

import aiohttp
from pydantic import BaseModel, Field


class FacetMatcherType(IntEnum):
    """FacetMatcher type enumeration."""

    EQUAL = 1  # = exact match
    NOT_EQUAL = 2  # != not equal match
    LIKE = 3  # LIKE regex match
    NOT_LIKE = 4  # NOT LIKE inverse regex match
    IN = 5  # IN contains match
    NOT_IN = 6  # NOT IN not contains match
    GREATER_THAN = 7  # > greater than comparison
    LESS_THAN = 8  # < less than comparison


class FacetName(str, Enum):
    """FacetName field name enumeration."""

    # Basic Span attributes
    SERVICE = "service"  # service name
    OPERATION = "operation"  # operation name/span name
    KIND = "kind"  # span type
    STATUS = "status"  # status code
    TRACE_ID = "traceId"  # trace ID
    IP = "ip"  # host IP address
    DURATION = "duration"  # duration
    SPAN_TYPE = "spanType"  # span type classification

    # HTTP related attributes
    HTTP_HOST = "http.host"  # HTTP request host
    HTTP_ROUTE = "http.route"  # HTTP route
    HTTP_METHOD = "http.method"  # HTTP method
    HTTP_RESP_CODE = "http.resp.code"  # HTTP response code

    # Exception related attributes
    EXCEPTION_TYPE = "exception.type"  # exception type
    EXCEPTION_MESSAGE = "exception.message"  # exception message

    # Database related attributes
    DB = "db"  # database system
    DB_NAME = "db.name"  # database name
    DB_TABLE = "db.table"  # database table name
    DB_OPERATION = "db.operation"  # database operation
    DB_STATEMENT = "db.statement"  # SQL statement


class FacetMatcher(BaseModel):
    """Query condition matcher."""

    name: FacetName = Field(..., description="Facet name for querying, such as service, operation, status")
    values: list[str] = Field(..., description="List of values to match against the facet")
    type: FacetMatcherType = Field(..., description="Type of matching operation to perform")


class SpanReference(BaseModel):
    """Span reference relationship."""

    refType: str = Field(..., description="Reference type, typically 'CHILD_OF' or 'FOLLOWS_FROM'")
    traceID: str = Field(..., description="Trace ID that this reference belongs to")
    spanID: str = Field(..., description="Span ID that this reference points to")


class Span(BaseModel):
    """Span data model."""

    traceID: str = Field(..., description="Trace ID that this span belongs to")
    spanID: str | None = Field(None, description="Unique identifier for this span")
    serviceName: str | None = Field(None, description="Name of the service that generated this span")
    operationName: str | None = Field(None, description="Operation name describing what this span does")
    spanType: str | None = Field(None, description="Type classification of the span (e.g., http, db, rpc)")
    requestHost: str | None = Field(None, description="HTTP request target host address")
    hostIP: str | None = Field(None, description="IP address of the host that initiated the request")
    scopeName: str | None = Field(None, description="OpenTelemetry instrumentation scope name")
    language: str | None = Field(None, description="Programming language used by the service")
    statusCode: str | None = Field(None, description="Status code of the operation (OK, ERROR, TIMEOUT)")
    startTime: int | None = Field(None, description="Span start time as Unix timestamp in microseconds")
    duration: int | None = Field(None, description="Span duration in microseconds")
    processID: str | None = Field(None, description="Process ID associated with this span")
    references: list[SpanReference] | None = Field(None, description="List of references to other spans")


class Process(BaseModel):
    """Process information."""

    serviceName: str = Field(..., description="Name of the service associated with this process")


class SearchSpansRequest(BaseModel):
    """Request model for searching spans by query conditions."""

    from_time: int = Field(..., description="Start time, unix timestamp in seconds")
    to_time: int = Field(..., description="End time, unix timestamp in seconds")
    matchers: list[FacetMatcher] | None = Field(None, description="List of query conditions")
    order_by: str = Field("duration", description="Sort field, default is duration")
    sort: str = Field("DESC", description="Sort order, DESC or ASC, default is DESC")
    size: int = Field(500, description="Limit of returned spans, default is 500")
    show_all_attributes: bool = Field(False, description="Whether to show span attributes, default is False")
    environment: str = Field("prod", description="Query environment, default is prod")
    timeout: int = Field(30, description="Request timeout in seconds, default is 30")


class GetTraceSpansRequest(BaseModel):
    """Request model for querying span list by TraceID."""

    trace_id: str = Field(..., description="Trace ID")
    from_time: int = Field(..., description="Start time, unix timestamp in seconds")
    to_time: int = Field(..., description="End time, unix timestamp in seconds")
    environment: str = Field("prod", description="Query environment, default is prod")
    timeout: int = Field(30, description="Request timeout in seconds, default is 30")


class TraceResponse(BaseModel):
    """Trace and span response data model."""

    spans: list[Span] = Field(..., description="List of span objects representing operations or calls")
    processes: dict[str, Process] = Field(..., description="Process information mapping, keyed by process ID")
    spanTypes: list[str] = Field(..., description="List of available span types in the system")


class SearchSpansResponse(TraceResponse):
    """Response data model for search_spans function."""

    pass


class GetTraceSpansResponse(TraceResponse):
    """Response data model for get_trace_spans function."""

    traceID: str | None = Field(None, description="Trace ID when querying by specific trace")


async def search_spans(request: SearchSpansRequest) -> SearchSpansResponse:
    """Search spans by query conditions.

    Args:
        request: SearchSpansRequest containing all parameters for span search

    Returns:
        SearchSpansResponse: Response data containing spans, processes, spanTypes

    Raises:
        aiohttp.ClientError: Network request error
        ValueError: Parameter validation error
        json.JSONDecodeError: JSON parse error

    Environment Variables:
        INSIGHTFLOW_BASE_URL: Span query API base URL
    """
    base_url = os.getenv("INSIGHTFLOW_BASE_URL")
    if not base_url:
        raise ValueError("Environment variable INSIGHTFLOW_BASE_URL is not set")

    if not base_url.startswith(("http://", "https://")):
        raise ValueError("INSIGHTFLOW_BASE_URL must start with http:// or https://")

    if request.from_time >= request.to_time:
        raise ValueError("Start time must be less than end time")

    url = f"{base_url.rstrip('/')}/api/v2/spans/search"

    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
        "insightflow-env": request.environment,
    }

    params = {"from": request.from_time, "to": request.to_time}

    body = {
        "orderBy": request.order_by,
        "sort": request.sort,
        "size": request.size,
        "showAllAttributes": request.show_all_attributes,
    }

    if request.matchers:
        body["matchers"] = [
            {"name": matcher.name, "type": matcher.type, "values": matcher.values} for matcher in request.matchers
        ]

    async with (
        aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=request.timeout)) as session,
        session.post(url, headers=headers, params=params, json=body) as response,
    ):
        response.raise_for_status()

        try:
            response.raise_for_status()
            data = await response.json()
        except aiohttp.ClientError as e:
            raise RuntimeError(f"search_spans request failed: {str(e)}") from e
        except ValueError as e:
            raise RuntimeError("Failed to parse search_spans response") from e

        if "data" not in data:
            raise ValueError("API response format error: missing data field")

        response_data = data["data"]

        spans = []
        for span_data in response_data.get("spans", []):
            references = None
            if "references" in span_data:
                references = [
                    SpanReference(
                        refType=ref.get("refType", ""),
                        traceID=ref.get("traceID", ""),
                        spanID=ref.get("spanID", ""),
                    )
                    for ref in span_data["references"]
                ]

            span = Span(
                traceID=span_data.get("traceID", ""),
                spanID=span_data.get("spanID"),
                serviceName=span_data.get("serviceName"),
                operationName=span_data.get("operationName"),
                spanType=span_data.get("spanType"),
                requestHost=span_data.get("requestHost"),
                hostIP=span_data.get("hostIP"),
                scopeName=span_data.get("scopeName"),
                language=span_data.get("language"),
                statusCode=span_data.get("statusCode"),
                startTime=span_data.get("startTime"),
                duration=span_data.get("duration"),
                processID=span_data.get("processID"),
                references=references,
            )
            spans.append(span)

        processes = {}
        for process_id, process_data in response_data.get("processes", {}).items():
            processes[process_id] = Process(serviceName=process_data.get("serviceName", ""))

        return SearchSpansResponse(
            spans=spans,
            processes=processes,
            spanTypes=response_data.get("spanTypes", []),
        )


async def get_trace_spans(request: GetTraceSpansRequest) -> GetTraceSpansResponse:
    """Query span list by TraceID.

    Args:
        request: GetTraceSpansRequest containing all parameters for trace query

    Returns:
        GetTraceSpansResponse: Response data containing spans, processes, spanTypes and traceID

    Raises:
        aiohttp.ClientError: Network request error
        ValueError: Parameter validation error
        json.JSONDecodeError: JSON parse error

    Environment Variables:
        INSIGHTFLOW_BASE_URL: Span query API base URL
    """
    base_url = os.getenv("INSIGHTFLOW_BASE_URL")
    if not base_url:
        raise ValueError("Environment variable INSIGHTFLOW_BASE_URL is not set")

    if not base_url.startswith(("http://", "https://")):
        raise ValueError("INSIGHTFLOW_BASE_URL must start with http:// or https://")

    if not request.trace_id:
        raise ValueError("trace_id cannot be empty")

    if request.from_time >= request.to_time:
        raise ValueError("Start time must be less than end time")

    url = f"{base_url.rstrip('/')}/api/v2/trace/{request.trace_id}"

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "insightflow-env": request.environment,
    }

    params = {"from": request.from_time, "to": request.to_time}

    async with (
        aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=request.timeout)) as session,
        session.get(url, headers=headers, params=params) as response,
    ):
        try:
            response.raise_for_status()
            data = await response.json()
        except aiohttp.ClientError as e:
            raise RuntimeError(f"get_trace_spans request failed: {str(e)}") from e
        except ValueError as e:
            raise RuntimeError("Failed to parse get_trace_spans response") from e

        if "data" not in data:
            raise ValueError("get_trace_spans response format error: missing data field")

        response_data = data["data"]

        spans = []
        for span_data in response_data.get("spans", []):
            references = None
            if "references" in span_data:
                references = [
                    SpanReference(
                        refType=ref.get("refType", ""), traceID=ref.get("traceID", ""), spanID=ref.get("spanID", "")
                    )
                    for ref in span_data["references"]
                ]

            span = Span(
                traceID=span_data.get("traceID", ""),
                spanID=span_data.get("spanID"),
                serviceName=span_data.get("serviceName"),
                operationName=span_data.get("operationName"),
                spanType=span_data.get("spanType"),
                requestHost=span_data.get("requestHost"),
                hostIP=span_data.get("hostIP"),
                scopeName=span_data.get("scopeName"),
                language=span_data.get("language"),
                statusCode=span_data.get("statusCode"),
                startTime=span_data.get("startTime"),
                duration=span_data.get("duration"),
                processID=span_data.get("processID"),
                references=references,
            )
            spans.append(span)

        processes = {}
        for process_id, process_data in response_data.get("processes", {}).items():
            processes[process_id] = Process(serviceName=process_data.get("serviceName", ""))

        return GetTraceSpansResponse(
            spans=spans,
            processes=processes,
            spanTypes=response_data.get("spanTypes", []),
            traceID=response_data.get("traceID"),
        )
