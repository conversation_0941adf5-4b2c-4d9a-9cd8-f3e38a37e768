"""InsightFlow API client for log queries."""

import os
from typing import Any

import aiohttp
from pydantic import BaseModel, Field


class QueryLogsRequest(BaseModel):
    """Request model for querying logs by search conditions."""

    cluster: str = Field(..., description="Business cluster (e.g.: cnhb01)")
    logstore: str = Field(..., description="Log store name (e.g.: default_li-log-adapter)")
    query: str = Field(..., description="Query statement (needs url encoding)")
    from_time: int = Field(..., description="Start time, unix timestamp in seconds")
    to_time: int = Field(..., description="End time, unix timestamp in seconds")
    environment: str = Field("prod", description="Query environment, default is prod")
    page: int = Field(1, description="Page number, default is 1")
    size: int = Field(100, description="Number of returned logs, default is 100")
    show_tag: bool = Field(False, description="Whether to return tag information, default is False")
    timeout: int = Field(30, description="Request timeout in seconds, default is 30")


class LogRecord(BaseModel):
    """Log record data model."""

    container_ip: str | None = Field(None, description="IP address of the container that generated the log")
    container_name: str | None = Field(None, description="Name of the container that generated the log")
    namespace_p: str | None = Field(None, description="Namespace/environment where the container is running")
    source: str | None = Field(None, description="Source of the log entry (e.g., stdout, stderr)")
    time: str | None = Field(None, description="Timestamp when the log entry was generated")

    # Extra log fields stored in this dictionary
    extra_fields: dict[str, Any] | None = Field(None, description="Additional log fields beyond the standard ones")


class LogsResponse(BaseModel):
    """Log query response data model."""

    result: list[LogRecord] = Field(..., description="List of log records matching the query criteria")


async def query_logs(request: QueryLogsRequest) -> LogsResponse:
    """Query logs by search conditions.

    Args:
        request: QueryLogsRequest containing all parameters for log query

    Returns:
        LogsResponse: Response data containing log record list

    Raises:
        aiohttp.ClientError: Network request error
        ValueError: Parameter validation error
        json.JSONDecodeError: JSON parse error

    Environment Variables:
        INSIGHTFLOW_LOG_BASE_URL: Log query API base URL
    """
    base_url = os.getenv("INSIGHTFLOW_LOG_BASE_URL")
    if not base_url:
        raise ValueError("Environment variable INSIGHTFLOW_LOG_BASE_URL is not set")

    if not base_url.startswith(("http://", "https://")):
        raise ValueError("INSIGHTFLOW_BASE_URL must start with http:// or https://")

    if not request.cluster:
        raise ValueError("cluster cannot be empty")

    if not request.logstore:
        raise ValueError("logstore cannot be empty")

    if request.from_time >= request.to_time:
        raise ValueError("Start time must be less than end time")

    url = f"{base_url.rstrip('/')}/datax/api/v1/logs"

    headers = {
        # "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "insightflow-env": request.environment,
    }

    body = {
        "cluster": request.cluster,
        "env": request.environment,
        "logstore": request.logstore,
        "page": request.page,
        "size": request.size,
        "query": request.query,
        "from": request.from_time,
        "to": request.to_time,
        "showTag": request.show_tag,
    }

    async with (
        aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=request.timeout)) as session,
        session.post(url, headers=headers, json=body) as response,
    ):
        try:
            response.raise_for_status()
            data = await response.json()
        except aiohttp.ClientError as e:
            raise RuntimeError(f"query_logs request failed: {str(e)}") from e
        except ValueError as e:
            raise RuntimeError("Failed to parse query_logs response") from e

        if "data" not in data:
            raise ValueError("query_logs response format error: missing data field")

        response_data = data["data"]

        logs = []
        for log_data in response_data.get("result", []):
            # Extract known fields
            log_record = LogRecord(
                container_ip=log_data.get("_container_ip"),
                container_name=log_data.get("_container_name"),
                namespace_p=log_data.get("_namespace_p"),
                source=log_data.get("_source_"),
                time=log_data.get("_time_"),
                extra_fields=None,
            )

            # Store other fields
            extra_fields = {}
            for key, value in log_data.items():
                if key not in [
                    "_container_ip",
                    "_container_name",
                    "_namespace_p",
                    "_source_",
                    "_time_",
                ]:
                    extra_fields[key] = value

            if extra_fields:
                log_record.extra_fields = extra_fields

            logs.append(log_record)

        return LogsResponse(result=logs)
