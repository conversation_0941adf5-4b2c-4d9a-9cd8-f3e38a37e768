"""robot tools"""


def get_user_authorization(search_type: str, user_id: str, service_ids: str) -> None:
    """robot obtains the user's authorization token"""
    pass


def create_group(title: str, scenario: str, id_type: str, ids: str) -> None:
    """robot creates a new session"""
    pass


def invite_user_to_group(id_type: str, ids: str, conversation_id: str) -> None:
    """robot invites users to join the conversation"""
    pass
