{"permissions": {"allow": ["Bash(cd /Users/<USER>/lixiang/gitlabee/deepheal/services/dhagent)", "Bash(make lint)", "<PERSON><PERSON>(make test)", "Bash(ls -la)", "Bash(cd services/dhagent)", "Bash(uv run ruff check .)", "Bash(uv run mypy src/)", "Bash(uv run pytest tests/test_change_manager.py -v)", "Bash(find tests -name \"*.py\" -type f)", "Bash(find services/dhagent -name \"*.py\" -exec wc -l {} +)", "Bash(uv run mypy src/ --no-error-summary)", "Bash(uv run ruff check src/ --output-format=text)", "Bash(uv run ruff check src/)", "Bash(uv run mypy src/usecases/change_management/)", "Bash(ls -la services/dhagent/)"], "deny": []}}