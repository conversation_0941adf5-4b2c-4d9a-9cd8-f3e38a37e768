-- 产品服务数据库表定义（理想汽车购车场景）
-- Product Service Database Schema for Li Auto

-- SPU表 (Standard Product Unit 标准产品单元)
-- 示例：L6, L7, L8, L9等车型
CREATE TABLE spus (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '车型名称，如：L6',
    brand VARCHAR(100) NOT NULL DEFAULT '理想汽车' COMMENT '品牌',
    series VARCHAR(100) NOT NULL COMMENT '车系，如：理想L6',
    description TEXT COMMENT '车型描述',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-上架，0-下架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_spus_brand_series (brand, series),
    INDEX idx_spus_status (status)
) COMMENT = 'SPU表-标准产品单元';

-- SKU表 (Stock Keeping Unit 库存保持单元)
-- 示例：L6-Pro, L6-Max
CREATE TABLE skus (
    id BIGSERIAL PRIMARY KEY,
    spu_id BIGINT NOT NULL COMMENT 'SPU ID',
    sku_code VARCHAR(100) NOT NULL COMMENT 'SKU编码，如：L6-Pro',
    name VARCHAR(255) NOT NULL COMMENT 'SKU名称，如：理想L6 Pro',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-可售，0-停售',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX uk_skus_sku_code (sku_code),
    INDEX idx_skus_spu_id (spu_id),
    INDEX idx_skus_status (status)
) COMMENT = 'SKU表-库存保持单元';

-- 配置分类表
-- 定义配置的大类：外观、轮毂、内饰、选装、核心标配等
CREATE TABLE configuration_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称，如：外观、轮毂、内饰、选装、核心标配',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    sort_order INTEGER DEFAULT 0 COMMENT '排序顺序',
    is_required SMALLINT DEFAULT 0 COMMENT '是否必选：1-必选，0-可选',
    is_multiple SMALLINT DEFAULT 0 COMMENT '是否可多选：1-可多选，0-单选',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-可用，0-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_configuration_categories_sort_order (sort_order),
    INDEX idx_configuration_categories_status (status)
) COMMENT = '配置分类表';

-- 配置表
-- 具体的配置项：白色珍珠漆、21英寸银灰轮毂等
CREATE TABLE configurations (
    id BIGSERIAL PRIMARY KEY,
    category_id BIGINT NOT NULL COMMENT '配置分类ID',
    name VARCHAR(255) NOT NULL COMMENT '配置名称，如：白色珍珠漆',
    display_name VARCHAR(255) NOT NULL COMMENT '显示名称',
    description TEXT COMMENT '配置详细描述',
    sort_order INTEGER DEFAULT 0 COMMENT '分类内排序',
    is_default SMALLINT DEFAULT 0 COMMENT '是否为默认配置：1-是，0-否',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-可用，0-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_configurations_category_id (category_id),
    INDEX idx_configurations_sort_order (sort_order),
    INDEX idx_configurations_status (status)
) COMMENT = '配置表';

-- SKU配置关联表
-- 定义某个SKU支持哪些配置
CREATE TABLE sku_configurations (
    id BIGSERIAL PRIMARY KEY,
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    configuration_id BIGINT NOT NULL COMMENT '配置ID',
    is_standard SMALLINT DEFAULT 0 COMMENT '是否为标配：1-标配，0-可选配',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX uk_sku_configurations_sku_config (sku_id, configuration_id),
    INDEX idx_sku_configurations_sku_id (sku_id),
    INDEX idx_sku_configurations_config_id (configuration_id)
) COMMENT = 'SKU配置关联表';

-- 示例数据插入语句（注释）
--
-- INSERT INTO spus (name, brand, series, description) VALUES
-- ('L6', '理想汽车', '理想L6', '理想汽车L6系列');
--
-- INSERT INTO skus (spu_id, sku_code, name) VALUES
-- (1, 'L6-Pro', '理想L6 Pro'),
-- (1, 'L6-Max', '理想L6 Max');
--
-- INSERT INTO configuration_categories (name, display_name, sort_order, is_required) VALUES
-- ('exterior', '外观', 1, 1),
-- ('wheel', '轮毂', 2, 1),
-- ('interior', '内饰', 3, 1),
-- ('optional', '选装', 4, 0),
-- ('standard', '核心标配', 5, 0);
--
-- INSERT INTO configurations (category_id, name, display_name, is_default) VALUES
-- (1, 'white_pearl', '白色珍珠漆', 1),
-- (1, 'gray_metallic', '灰色金属漆', 0),
-- (2, '21_inch_silver', '21英寸银灰', 1),
-- (2, '21_inch_black', '全新21寸黑色双色', 0),
-- (3, 'black_interior', '黑色双色内饰', 1),
-- (3, 'black_orange_interior', '黑橙双色内饰', 0),
-- (4, 'electric_step', '电动踏板', 0),
-- (5, 'battery_upgrade', '升级至52.3度大电池', 0),
-- (5, 'air_suspension', '双腔魔毯空气悬架', 0);
