openapi: 3.0.3
info:
  title: Product Service API
  description: 理想汽车产品服务API - 管理SPU、SKU和配置信息
  version: 1.0.0
  contact:
    name: Product Service Team

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发环境

paths:
  /spus:
    get:
      tags:
        - SPU管理
      summary: 获取车型SPU列表
      description: 获取所有可售车型的SPU列表信息
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: status
          in: query
          description: 状态筛选
          required: false
          schema:
            type: integer
            enum: [0, 1]
            description: 0-下架，1-上架
      responses:
        '200':
          description: 成功获取SPU列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/SpuResponse'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

  /spus/{spuId}:
    get:
      tags:
        - SPU管理
      summary: 根据SPU ID获取SPU信息
      description: 通过SPU ID获取指定车型的详细信息
      parameters:
        - name: spuId
          in: path
          required: true
          description: SPU ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取SPU信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SpuResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /spus/{spuId}/skus:
    get:
      tags:
        - SKU管理
      summary: 通过SPU ID获取车型SKU列表
      description: 根据SPU ID获取该车型下的所有SKU列表
      parameters:
        - name: spuId
          in: path
          required: true
          description: SPU ID
          schema:
            type: integer
            format: int64
        - name: status
          in: query
          description: 状态筛选
          required: false
          schema:
            type: integer
            enum: [0, 1]
            description: 0-停售，1-可售
      responses:
        '200':
          description: 成功获取SKU列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/SkuResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /skus/{skuId}:
    get:
      tags:
        - SKU管理
      summary: 根据SKU ID获取SKU信息
      description: 通过SKU ID获取指定SKU的详细信息
      parameters:
        - name: skuId
          in: path
          required: true
          description: SKU ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取SKU信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SkuResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /skus/{skuId}/configurations:
    get:
      tags:
        - 配置管理
      summary: 通过SKU ID获取车型SKU配置信息
      description: 根据SKU ID获取该SKU支持的所有配置信息，按分类组织
      parameters:
        - name: skuId
          in: path
          required: true
          description: SKU ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取SKU配置信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ConfigurationCategoryResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /configurations:
    get:
      tags:
        - 配置管理
      summary: 根据配置ID列表获取配置信息
      description: 批量获取指定配置ID列表的配置详情
      parameters:
        - name: configurationIds
          in: query
          required: true
          description: 配置ID列表，用逗号分隔
          schema:
            type: string
            example: "1,2,3"
      responses:
        '200':
          description: 成功获取配置列表信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ConfigurationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

components:
  schemas:
    SpuResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: SPU ID
        name:
          type: string
          description: 车型名称
          example: "L6"
        brand:
          type: string
          description: 品牌
          example: "理想汽车"
        series:
          type: string
          description: 车系
          example: "理想L6"
        description:
          type: string
          description: 车型描述
        status:
          type: integer
          description: 状态
          enum: [0, 1]
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    SkuResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: SKU ID
        spuId:
          type: integer
          format: int64
          description: SPU ID
        skuCode:
          type: string
          description: SKU编码
          example: "L6-Pro"
        name:
          type: string
          description: SKU名称
          example: "理想L6 Pro"
        status:
          type: integer
          description: 状态
          enum: [0, 1]
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    ConfigurationCategoryResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 分类ID
        name:
          type: string
          description: 分类名称
          example: "exterior"
        displayName:
          type: string
          description: 显示名称
          example: "外观"
        sortOrder:
          type: integer
          description: 排序顺序
        isRequired:
          type: integer
          description: 是否必选
          enum: [0, 1]
        isMultiple:
          type: integer
          description: 是否可多选
          enum: [0, 1]
        configurations:
          type: array
          description: 该分类下的配置列表
          items:
            $ref: '#/components/schemas/ConfigurationResponse'

    ConfigurationResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 配置ID
        categoryId:
          type: integer
          format: int64
          description: 分类ID
        name:
          type: string
          description: 配置名称
          example: "white_pearl"
        displayName:
          type: string
          description: 显示名称
          example: "白色珍珠漆"
        description:
          type: string
          description: 配置详细描述
        sortOrder:
          type: integer
          description: 分类内排序
        isDefault:
          type: integer
          description: 是否为默认配置
          enum: [0, 1]
        isStandard:
          type: integer
          description: 是否为标配
          enum: [0, 1]
        status:
          type: integer
          description: 状态
          enum: [0, 1]

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        size:
          type: integer
          description: 每页数量

    Error:
      type: object
      properties:
        code:
          type: string
          description: 错误代码
        message:
          type: string
          description: 错误信息
        details:
          type: array
          items:
            type: object
            properties:
              "@type":
                type: string
              field:
                type: string
              reason:
                type: string

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INVALID_ARGUMENT"
            message: "请求包含无效参数"
            details:
              - "@type": "ValidationError"
                field: "spuId"
                reason: "REQUIRED"

    NotFound:
      description: 资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "NOT_FOUND"
            message: "请求的资源不存在"

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INTERNAL_ERROR"
            message: "服务器内部错误"

tags:
  - name: SPU管理
    description: 标准产品单元管理相关接口
  - name: SKU管理
    description: 库存保持单元管理相关接口
  - name: 配置管理
    description: 产品配置管理相关接口
