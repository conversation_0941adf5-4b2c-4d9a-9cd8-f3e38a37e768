-- 定价服务数据库表定义（简化版 - 理想汽车购车场景）
-- Pricing Service Database Schema for Li Auto (Simplified)

-- SKU定价策略表
-- 管理不同SKU（如L6-Pro, L6-Max）的定价策略
CREATE TABLE sku_pricing_strategies (
    id BIGSERIAL PRIMARY KEY,
    sku_id BIGINT NOT NULL COMMENT 'SKU ID，对应product-service的skus表',
    strategy_name VARCHAR(255) NOT NULL COMMENT '策略名称，如：上市价、促销价等',
    strategy_price DECIMAL(10,2) NOT NULL COMMENT 'SKU策略价格',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX uk_sku_pricing_strategies_sku_effective (sku_id, effective_date),
    INDEX idx_sku_pricing_strategies_sku_status (sku_id, status),
    INDEX idx_sku_pricing_strategies_effective_date (effective_date)
) COMMENT = 'SKU定价策略表';

-- SKU配置定价策略表
-- 管理具体配置项（如白色珍珠漆、21英寸轮毂等）的定价策略
CREATE TABLE sku_configuration_pricing_strategies (
    id BIGSERIAL PRIMARY KEY,
    sku_id BIGINT NOT NULL COMMENT 'SKU ID，对应product-service的skus表',
    configuration_id BIGINT NOT NULL COMMENT '配置ID，对应product-service的configurations表',
    strategy_name VARCHAR(255) NOT NULL COMMENT '策略名称',
    strategy_price DECIMAL(10,2) NOT NULL COMMENT '配置策略价格',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status SMALLINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX uk_sku_config_pricing_sku_config_effective (sku_id, configuration_id, effective_date),
    INDEX idx_sku_config_pricing_sku_config (sku_id, configuration_id),
    INDEX idx_sku_config_pricing_effective_date (effective_date)
) COMMENT = 'SKU配置定价策略表';

-- 定价计算记录表
-- 记录具体的定价计算过程和结果
CREATE TABLE pricing_calculations (
    id BIGSERIAL PRIMARY KEY,
    calculation_id VARCHAR(50) NOT NULL COMMENT '计算ID',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    configuration_ids JSON COMMENT '选择的配置ID列表',
    sku_strategy_price DECIMAL(10,2) NOT NULL COMMENT 'SKU策略定价',
    configuration_strategy_total_price DECIMAL(10,2) DEFAULT 0 COMMENT '配置策略定价总和',
    final_price DECIMAL(10,2) NOT NULL COMMENT '最终价格 = SKU策略定价 + 配置策略定价总和',
    pricing_details JSON COMMENT '定价明细（包含各配置的策略价格）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE INDEX uk_pricing_calculations_calculation_id (calculation_id),
    INDEX idx_pricing_calculations_sku_id (sku_id),
    INDEX idx_pricing_calculations_created_at (created_at)
) COMMENT = '定价计算记录表';

-- 示例数据插入语句（注释）
--
-- -- SKU定价策略示例
-- INSERT INTO sku_pricing_strategies (sku_id, strategy_name, strategy_price, effective_date) VALUES
-- (1, '上市价', 249800.00, '2024-01-01'),  -- L6-Pro
-- (2, '上市价', 279800.00, '2024-01-01');  -- L6-Max
--
-- -- SKU配置定价策略示例
-- INSERT INTO sku_configuration_pricing_strategies (sku_id, configuration_id, strategy_name, strategy_price, effective_date) VALUES
-- -- L6-Pro的配置定价
-- (1, 1, '标准价', 0.00, '2024-01-01'),      -- 白色珍珠漆
-- (1, 2, '标准价', 2000.00, '2024-01-01'),   -- 灰色金属漆
-- (1, 3, '标准价', 0.00, '2024-01-01'),      -- 21英寸银灰
-- (1, 4, '标准价', 3000.00, '2024-01-01'),   -- 全新21寸黑色双色
-- (1, 5, '标准价', 0.00, '2024-01-01'),      -- 黑色双色内饰
-- (1, 6, '标准价', 5000.00, '2024-01-01'),   -- 黑橙双色内饰
-- (1, 7, '标准价', 8000.00, '2024-01-01'),   -- 电动踏板
-- (1, 8, '标准价', 0.00, '2024-01-01'),      -- 升级至52.3度大电池
-- (1, 9, '标准价', 0.00, '2024-01-01'),      -- 双腔魔毯空气悬架
-- -- L6-Max的配置定价（可能有不同的策略价格）
-- (2, 1, '标准价', 0.00, '2024-01-01'),      -- 白色珍珠漆
-- (2, 2, '标准价', 2000.00, '2024-01-01'),   -- 灰色金属漆
-- (2, 3, '标准价', 0.00, '2024-01-01'),      -- 21英寸银灰
-- (2, 4, '标准价', 3000.00, '2024-01-01'),   -- 全新21寸黑色双色
-- (2, 5, '标准价', 0.00, '2024-01-01'),      -- 黑色双色内饰
-- (2, 6, '标准价', 5000.00, '2024-01-01'),   -- 黑橙双色内饰
-- (2, 7, '标准价', 8000.00, '2024-01-01'),   -- 电动踏板
-- (2, 8, '标准价', 0.00, '2024-01-01'),      -- 升级至52.3度大电池
-- (2, 9, '标准价', 0.00, '2024-01-01');      -- 双腔魔毯空气悬架
