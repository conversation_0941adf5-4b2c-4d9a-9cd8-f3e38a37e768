openapi: 3.0.3
info:
  title: Pricing Service API
  description: 理想汽车定价服务API - 管理SKU和配置的定价策略
  version: 1.0.0
  contact:
    name: Pricing Service Team

servers:
  - url: http://localhost:8081/api/v1
    description: 本地开发环境

paths:
  /calculations:
    post:
      tags:
        - 定价计算
      summary: 生成定价
      description: 根据SKU ID和选择的配置ID列表，计算最终价格
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PricingCalculationRequest'
      responses:
        '200':
          description: 成功计算定价
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PricingCalculationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /calculations/{calculationId}:
    get:
      tags:
        - 定价计算
      summary: 获取定价计算结果
      description: 根据计算ID获取之前的定价计算结果
      parameters:
        - name: calculationId
          in: path
          required: true
          description: 计算ID
          schema:
            type: string
      responses:
        '200':
          description: 成功获取定价计算结果
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PricingCalculationResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /skus/{skuId}/price:
    get:
      tags:
        - SKU定价
      summary: 获取SKU价格
      description: 根据SKU ID获取当前的策略价格
      parameters:
        - name: skuId
          in: path
          required: true
          description: SKU ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取SKU价格
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/SkuPriceResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  /configurations/prices:
    get:
      tags:
        - 配置定价
      summary: 批量获取配置价格
      description: 根据配置ID列表批量获取配置的当前策略价格
      parameters:
        - name: configurationIds
          in: query
          required: true
          description: 配置ID列表，用逗号分隔
          schema:
            type: string
            example: "1,2,3,4"
      responses:
        '200':
          description: 成功获取配置价格列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ConfigurationPriceResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

components:
  schemas:
    PricingCalculationRequest:
      type: object
      required:
        - skuId
        - configurationIds
      properties:
        skuId:
          type: integer
          format: int64
          description: SKU ID
          example: 1
        configurationIds:
          type: array
          items:
            type: integer
            format: int64
          description: 选择的配置ID列表
          example: [1, 3, 5, 7]

    PricingCalculationResponse:
      type: object
      properties:
        calculationId:
          type: string
          description: 计算ID
          example: "calc_20240101_123456"
        skuId:
          type: integer
          format: int64
          description: SKU ID
        skuStrategyPrice:
          type: number
          format: decimal
          description: SKU策略定价
          example: 249800.00
        configurationStrategyTotalPrice:
          type: number
          format: decimal
          description: 配置策略定价总和
          example: 15000.00
        finalPrice:
          type: number
          format: decimal
          description: 最终价格
          example: 264800.00
        pricingDetails:
          type: array
          description: 定价明细
          items:
            $ref: '#/components/schemas/PricingDetail'
        createdAt:
          type: string
          format: date-time
          description: 创建时间

    PricingDetail:
      type: object
      properties:
        configurationId:
          type: integer
          format: int64
          description: 配置ID
        configurationName:
          type: string
          description: 配置名称
          example: "灰色金属漆"
        strategyPrice:
          type: number
          format: decimal
          description: 策略价格
          example: 2000.00

    SkuPriceResponse:
      type: object
      properties:
        skuId:
          type: integer
          format: int64
          description: SKU ID
        strategyPrice:
          type: number
          format: decimal
          description: SKU策略价格
          example: 249800.00
        effectiveFrom:
          type: string
          format: date-time
          description: 生效时间
        effectiveTo:
          type: string
          format: date-time
          description: 失效时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    ConfigurationPriceResponse:
      type: object
      properties:
        configurationId:
          type: integer
          format: int64
          description: 配置ID
        strategyPrice:
          type: number
          format: decimal
          description: 配置策略价格
          example: 2000.00
        effectiveFrom:
          type: string
          format: date-time
          description: 生效时间
        effectiveTo:
          type: string
          format: date-time
          description: 失效时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    Error:
      type: object
      properties:
        code:
          type: string
          description: 错误代码
        message:
          type: string
          description: 错误信息
        details:
          type: array
          items:
            type: object
            properties:
              "@type":
                type: string
              field:
                type: string
              reason:
                type: string

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INVALID_ARGUMENT"
            message: "请求包含无效参数"
            details:
              - "@type": "ValidationError"
                field: "skuId"
                reason: "REQUIRED"

    NotFound:
      description: 资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "NOT_FOUND"
            message: "请求的资源不存在"

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INTERNAL_ERROR"
            message: "服务器内部错误"

tags:
  - name: 定价计算
    description: 定价计算相关接口
  - name: SKU定价
    description: SKU价格查询接口
  - name: 配置定价
    description: 配置价格查询接口
