openapi: 3.0.3
info:
  title: Order Service API
  description: 理想汽车订单服务API - 管理用户下单和订单状态
  version: 1.0.0
  contact:
    name: Order Service Team

servers:
  - url: http://localhost:8082/api/v1
    description: 本地开发环境

paths:
  /orders:
    post:
      tags:
        - 订单管理
      summary: 下单
      description: 创建新订单，使用预先计算的定价结果。客户端需要先调用pricing-service进行定价计算，然后使用返回的计算ID创建订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '200':
          description: 订单创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/OrderResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

    get:
      tags:
        - 订单管理
      summary: 获取订单列表
      description: 获取用户的订单列表，支持分页和状态筛选
      parameters:
        - name: userId
          in: query
          required: true
          description: 用户ID
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: orderStatus
          in: query
          description: 订单状态筛选
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            description: 1-待处理，2-已确认，3-已取消
        - name: paymentStatus
          in: query
          description: 支付状态筛选
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            description: 1-未支付，2-已支付，3-已退款
      responses:
        '200':
          description: 成功获取订单列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/OrderResponse'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

  /orders/{orderId}:
    get:
      tags:
        - 订单管理
      summary: 获取订单详情
      description: 根据订单ID获取订单的详细信息，包括配置详情
      parameters:
        - name: orderId
          in: path
          required: true
          description: 订单ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取订单详情
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/OrderDetailResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'


components:
  schemas:
    CreateOrderRequest:
      type: object
      required:
        - userId
        - pricingCalculationId
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 12345
        pricingCalculationId:
          type: string
          description: 定价计算ID，来自pricing-service的计算结果
          example: "calc_20240101_123456"


    OrderResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 订单ID
        orderNo:
          type: string
          description: 订单号
          example: "ORD202401010001"
        userId:
          type: integer
          format: int64
          description: 用户ID
        spuId:
          type: integer
          format: int64
          description: SPU ID
        spuName:
          type: string
          description: SPU名称
          example: "L6"
        skuId:
          type: integer
          format: int64
          description: SKU ID
        skuName:
          type: string
          description: SKU名称
          example: "理想L6 Pro"
        basePrice:
          type: number
          format: decimal
          description: 基础价格
          example: 249800.00
        configurationPrice:
          type: number
          format: decimal
          description: 配置总价格
          example: 15000.00
        totalPrice:
          type: number
          format: decimal
          description: 订单总价
          example: 264800.00
        pricingCalculationId:
          type: integer
          format: int64
          description: 定价计算记录ID，用于追溯计费差异
          example: 123456
        orderStatus:
          type: integer
          description: 订单状态
          enum: [1, 2, 3]
        orderStatusText:
          type: string
          description: 订单状态文本
          example: "待处理"
        paymentStatus:
          type: integer
          description: 支付状态
          enum: [1, 2, 3]
        paymentStatusText:
          type: string
          description: 支付状态文本
          example: "未支付"
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    OrderDetailResponse:
      allOf:
        - $ref: '#/components/schemas/OrderResponse'
        - type: object
          properties:
            configurations:
              type: array
              description: 订单配置详情列表
              items:
                $ref: '#/components/schemas/OrderConfigurationResponse'

    OrderConfigurationResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 订单配置ID
        configurationId:
          type: integer
          format: int64
          description: 配置ID
        configurationName:
          type: string
          description: 配置名称
          example: "灰色金属漆"
        configurationPrice:
          type: number
          format: decimal
          description: 配置价格
          example: 2000.00
        createdAt:
          type: string
          format: date-time
          description: 创建时间

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        size:
          type: integer
          description: 每页数量

    Error:
      type: object
      properties:
        code:
          type: string
          description: 错误代码
        message:
          type: string
          description: 错误信息
        details:
          type: array
          items:
            type: object
            properties:
              "@type":
                type: string
              field:
                type: string
              reason:
                type: string

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          examples:
            validation_error:
              summary: 参数验证错误
              value:
                code: "INVALID_ARGUMENT"
                message: "请求包含无效参数"
                details:
                  - "@type": "ValidationError"
                    field: "userId"
                    reason: "REQUIRED"
            pricing_calculation_error:
              summary: 定价计算ID无效
              value:
                code: "PRICING_CALCULATION_NOT_FOUND"
                message: "定价计算结果不存在或已失效"
                details:
                  - "@type": "ValidationError"
                    field: "pricingCalculationId"
                    reason: "INVALID_CALCULATION_ID"

    NotFound:
      description: 资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "NOT_FOUND"
            message: "请求的资源不存在"

    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INTERNAL_ERROR"
            message: "服务器内部错误"

tags:
  - name: 订单管理
    description: 订单相关接口，包括下单、查询、状态更新等
