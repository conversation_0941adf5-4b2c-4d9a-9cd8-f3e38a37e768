-- 订单服务数据库表定义
-- Order Service Database Schema

-- 订单表
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    spu_id BIGINT NOT NULL COMMENT 'SPU ID',
    spu_name VARCHAR(255) NOT NULL COMMENT 'SPU名称，如：L6',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    sku_name VARCHAR(255) NOT NULL COMMENT 'SKU名称，如：理想L6 Pro',
    base_price DECIMAL(10,2) NOT NULL COMMENT '基础价格',
    configuration_price DECIMAL(10,2) DEFAULT 0 COMMENT '配置总价格',
    total_price DECIMAL(10,2) NOT NULL COMMENT '订单总价',
    pricing_calculation_id BIGINT COMMENT '定价计算记录ID，关联pricing_calculations表',
    order_status SMALLINT DEFAULT 1 COMMENT '订单状态：1-待处理，2-已确认，3-已取消',
    payment_status SMALLINT DEFAULT 1 COMMENT '支付状态：1-未支付，2-已支付，3-已退款',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE INDEX uk_orders_order_no (order_no),
    INDEX idx_orders_user_id (user_id),
    INDEX idx_orders_pricing_calculation_id (pricing_calculation_id),
    INDEX idx_orders_order_status (order_status),
    INDEX idx_orders_payment_status (payment_status),
    INDEX idx_orders_created_at (created_at)
) COMMENT = '订单表';

-- 订单状态枚举说明
-- order_status: 1-待处理(PENDING), 2-已确认(CONFIRMED), 3-已取消(CANCELLED)
-- payment_status: 1-未支付(UNPAID), 2-已支付(PAID), 3-已退款(REFUNDED)

-- 订单配置详情表
CREATE TABLE order_configurations (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    configuration_id BIGINT NOT NULL COMMENT '配置ID',
    configuration_name VARCHAR(255) NOT NULL COMMENT '配置名称',
    configuration_price DECIMAL(10,2) NOT NULL COMMENT '配置价格',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_order_configurations_order_id (order_id)
) COMMENT = '订单配置详情表';
