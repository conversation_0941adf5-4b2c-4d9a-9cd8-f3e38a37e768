# 系统概述
仿真系统实现了理想汽车的在线选车和下单功能，主要包含三大核心服务：订单服务、定价服务和产品服务，并使用Redis作为缓存层提升系统性能。
# 核心流程
```mermaid
graph TD
    User[用户]

    subgraph 产品服务
        PS1[获取车型SPU列表接口]
        PS2[通过SPU ID获取车型SKU列表接口]
        PS3[通过SKU ID获取车型SKU配置信息接口]
        PS4[根据SPU ID获取SPU信息接口]
        PS5[根据SKU ID获取SKU信息接口]
        PS6[根据SKU配置ID列表获取SKU配置列表信息接口]
    end

    subgraph 定价服务
        PR1[生成定价接口]
    end

    subgraph 订单服务
        OS1[下单接口]
    end

    Redis[(Redis缓存)]

    %% 选车流程
    User -->|1.获取车型列表| PS1
    User -->|2.选择SPU,获取SKU列表| PS2
    User -->|3.选择SKU,获取配置信息| PS3
    User -->|4.获取定价| PR1
    PR1 -->|4.1验证SKU信息| PS5
    PR1 -->|4.2验证配置信息| PS6
    User -->|5.立即订购| OS1

    %% 下单流程 - 订单服务内部调用
    OS1 -->|1.验证价格| PR1

    %% 缓存交互
    PS1 -.->|缓存车型列表数据| Redis
    PS2 -.->|缓存SKU列表数据| Redis
    PS3 -.->|缓存配置列表数据| Redis
    PR1 -.->|缓存SKU数据| Redis
    PR1 -.->|缓存配置数据| Redis
    PR1 -.->|缓存价格数据| Redis


    style User fill:#f9f,stroke:#333,stroke-width:2px
    style Redis fill:#ff9999,stroke:#333,stroke-width:2px
```

# 接口设计约束
- HTTP 请求与响应的语义首先应该符合 RFC 9110
- 关于如何正确使用 HTTP status code 的描述推荐遵守，但 status code 首位数字使用必须符合相关定义（即 1xx，2xx，3xx，4xx，5xx）；
- 在 RFC 9110 的基础上：
  - 推荐使用以资源为中心，设计清晰的 URI；
    - URI 中使用名词表示资源，不应该使用动词，特殊情况可以参考附录；
    - 每个资源必须有唯一的 URI，资源集合也应该有自己的 URI；
- URI path 中的名词词组必须统一使用 kebab-case，URI query string 以及请求与响应 body 中的名词词组必须统一使用 camelCase，资源集合推荐使用复数形式
- 分页请求
  - 请求参数推荐放在 query string 中，参数名称必须为：
    - page：页数
    - size：每页大小

# 接口响应示例
- 200 状态码正常响应
```
{
  "data": {
    "id": 123,
    "title": "测试文章",
    "content": "这是文章内容"
  }
}
```
- 400 参数错误响应
```
{
  "code": "INVALID_ARGUMENT",
  "message": "请求包含多个无效字段",
  "details": [
    {
      "@type": "ValidationError",
      "field": "title",
      "reason": "REQUIRED"
    },
    {
      "@type": "ValidationError",
      "field": "email",
      "reason": "INVALID_FORMAT"
    }
  ]
}
```
# 仿真环境数据库设计约束
以下所有规范会按照【强制】、【推荐】两个级别进行标注，遵守优先级从高到低。 对于不满足【强制】级别的设计的，建议业务进行修改
## 命名规范
- 【强制】库名、表名限制命名长度，建议表名及字段名字符总长度小于等于63。(如果超过此限制长度，名称会被自动truncate为63个字符之内)
- 【强制】对象名（表名、列名、函数名、视图名、序列名、等对象名称）规范，对象名务必只使用小写字母，下划线，数字。不要以pg开头，不要以数字开头。(如果使用大写字母，会自动转化为小写，除非使用双引号特别标注)
- 【强制】对象名（表名、列名、函数名、视图名、序列名、等对象名称）规范，不要使用数据库保留字
- 【推荐】主键索引应以 pk_ 开头， 唯一索引要以 uk_ 开头，普通索引要以 idx_ 打头。(意义更明确)
- 【推荐】中间表用于保留中间结果集，名称必须以"tmp_"开头， 备份表用于备份或抓取源表快照，名称必须以"bak_"开头
- 【推荐】库名最好与应用名称一致，或便于辨识。
- 【推荐】使用默认的public schema，否则容易造成单库内对象和权限混乱，不同业务按库区分
## 表设计
- 【推荐】时间类型尽量选取datetime
- 【推荐】组合索引字段数不建议超过5个
- 【强制】多表中的关联列，数据类型要保持一致。
- 【强制】表要添加主键。
- 【强制】可以考虑使用自增序列作为主键，并且使用bigserial类型，避免int类型大于21亿使用上限的问题。
- 【强制】建表必须有comment
- 【强制】禁止使用外键，自定义函数，触发器
- 【强制】使用分区表不用使用二级分区，否则容易有内存问题，另外建议通过时间分区，可以方便摘分区归档或者删除
- 【强制】修改表结构注意，使用强制类型转化的列类型修改，会导致表重写，阻塞表上业务，比如int改varchar，int改bigint，varchar长度增加这类没有影响
- 【强制】创建索引的时候，要使用并行关键字CONCURRENTLY，不堵塞表的DML，否则会堵塞DML操作。
  - 比如：
  create index CONCURRENTLY ind_tab01_col01 on tab01(col01);
- 【强制】同一业务的表不要放在不同数据库中，不像MySQL可以跨库查询，PostgreSQL不能跨库查询，除非设置外表，但是太繁琐
- 【强制】线上表结构的变更包括添加字段，索引操作在业务低峰期进行

# 理想汽车产品SPU示例
  - SPU: L6
  - SKU: L6-Pro, L6-Max
  - 配置分类示例：
    - 外观：白色珍珠漆、灰色金属漆
    - 轮毂：21英寸银灰、全新21寸黑色双色
    - 内饰：黑色双色内饰、黑橙双色内饰
    - 选装：电动踏板
    - 核心标配：升级至52.3度大电池、双腔魔毯空气悬架
