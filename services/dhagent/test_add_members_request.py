#!/usr/bin/env python3
"""
Test script to verify AddMembersRequest field name changes.
"""

from src.entities.robot import AddMembersRequest, IdType


def test_add_members_request_fields():
    """Test that AddMembersRequest uses the correct field names."""
    print("🔍 Testing AddMembersRequest field names...")
    
    # Create request with new field names
    request = AddMembersRequest(
        id_type=IdType.LDAP_NAME,
        ids=["user1", "user2", "user3"]
    )
    
    print("✅ AddMembersRequest created successfully")
    
    # Verify field access
    assert request.id_type == IdType.LDAP_NAME
    assert request.ids == ["user1", "user2", "user3"]
    
    print(f"✅ id_type: {request.id_type}")
    print(f"✅ ids: {request.ids}")
    
    # Test serialization
    request_dict = request.model_dump()
    expected_dict = {
        "id_type": "ldap_name",
        "ids": ["user1", "user2", "user3"]
    }
    
    assert request_dict == expected_dict
    print(f"✅ Serialization correct: {request_dict}")
    
    # Test deserialization
    request_from_dict = AddMembersRequest.model_validate(expected_dict)
    assert request_from_dict.id_type == IdType.LDAP_NAME
    assert request_from_dict.ids == ["user1", "user2", "user3"]
    
    print("✅ Deserialization correct")
    
    print("\n🎉 All AddMembersRequest field tests passed!")


def test_old_field_names_removed():
    """Test that old field names are no longer accessible."""
    print("\n🔍 Testing that old field names are removed...")
    
    request = AddMembersRequest(
        id_type=IdType.FEISHU_USER_ID,
        ids=["feishu_user_1", "feishu_user_2"]
    )
    
    # Try to access old field names (should fail)
    try:
        _ = request.id_type
        print("❌ Old field 'id_type' still accessible!")
        return False
    except AttributeError:
        print("✅ Old field 'id_type' properly removed")
    
    try:
        _ = request.ids
        print("❌ Old field 'ids' still accessible!")
        return False
    except AttributeError:
        print("✅ Old field 'ids' properly removed")
    
    print("🎉 Old field names successfully removed!")
    return True


def test_api_request_format():
    """Test that the API request format is correct."""
    print("\n🔍 Testing API request format...")
    
    request = AddMembersRequest(
        id_type=IdType.LDAP_NAME,
        ids=["testuser1", "testuser2"]
    )
    
    # Simulate how HTTP client would format the request
    api_request = {
        "id_type": request.id_type.value,
        "ids": request.ids
    }
    
    expected_api_request = {
        "id_type": "ldap_name",
        "ids": ["testuser1", "testuser2"]
    }
    
    assert api_request == expected_api_request
    print(f"✅ API request format correct: {api_request}")
    
    print("🎉 API request format test passed!")


def main():
    """Run all tests."""
    print("🚀 Testing AddMembersRequest Field Name Changes")
    print("=" * 50)
    
    try:
        test_add_members_request_fields()
        success = test_old_field_names_removed()
        test_api_request_format()
        
        if success:
            print("\n" + "=" * 50)
            print("🎯 Summary:")
            print("• AddMembersRequest now uses 'id_type' instead of 'id_type'")
            print("• AddMembersRequest now uses 'ids' instead of 'ids'")
            print("• Old field names are no longer accessible")
            print("• API request format is correctly mapped")
            print("• All related code has been updated")
            print("\n✅ Field name changes completed successfully!")
        else:
            print("\n❌ Some tests failed!")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
