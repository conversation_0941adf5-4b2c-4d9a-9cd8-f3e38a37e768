# DeepHeal Agent

A Python-based AIOps application.

## Development Setup

### Prerequisites

- Python 3.12 or higher
- [uv](https://docs.astral.sh/uv/) - Python package manager

### Installing uv

If you don't have `uv` installed, you can install it using:

```bash
# On macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Or using pip
pip install uv
```

### Environment Setup

1. **Clone the repository and navigate to the project directory:**
   ```bash
   cd services/dhagent
   ```

2. **Install dependencies:**
   ```bash
   uv sync
   ```
   This will create a virtual environment and install all dependencies specified in `pyproject.toml`.

3. **Activate the virtual environment:**
   ```bash
   source .venv/bin/activate
   ```
   Or on Windows:
   ```bash
   .venv\Scripts\activate
   ```

4. **Verify the installation:**
   ```bash
   uv run python --version
   ```
   This should output Python 3.12.x.

5. **Set up development tools:**
   ```bash
   # Install development dependencies (includes linting tools)
   uv sync --group dev --group test
   ```

   **Note:** Pre-commit hooks are managed at the repository root level.
   See the [main README](../../README.md#code-quality--pre-commit-setup-) for pre-commit setup instructions.

### Running the Application

To run the FastAPI application:

```bash
# Using uv (recommended)
uv run python main.py

# Or using uvicorn directly
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

The application will start on `http://localhost:8000`. You can access:

- **API Documentation (Swagger UI):** `http://localhost:8000/docs`
- **Alternative API Documentation (ReDoc):** `http://localhost:8000/redoc`
- **Root endpoint:** `http://localhost:8000/`
- **Health check:** `http://localhost:8000/health`
- **Application info:** `http://localhost:8000/info`

## Docker Deployment

### Prerequisites

- Docker

### Building and Running with Docker

1. **Build the Docker image:**
   ```bash
   docker build -t dhagent:latest .
   ```

2. **Run the container:**
   ```bash
   docker run -d -p 8000:8000 --name dhagent dhagent:latest
   ```

3. **Check container status:**
   ```bash
   docker ps
   docker logs dhagent
   ```

### Docker Features

- **Multi-stage build** for optimized image size
- **Gunicorn with Uvicorn workers** for production-grade performance
- **Non-root user** for enhanced security
- **Health checks** for container orchestration
- **Proper logging** to stdout/stderr for container environments

### Adding Dependencies

To add new dependencies to the project:

```bash
# Add a regular dependency
uv add package_name

# Add a development dependency
uv add --dev package_name
```

### Development Commands

#### Quick Setup for New Developers
```bash
# Complete setup in one go
uv sync --group dev --group test    # Install all dependencies
uv run pytest                       # Run tests to ensure everything works

# For pre-commit setup, see the main repository README
```

#### Daily Commands
- **Run the FastAPI server:** `uv run python main.py`
- **Run with auto-reload:** `uv run uvicorn main:app --reload`
- **Install dependencies:** `uv sync`
- **Add new dependency:** `uv add package_name`
- **Remove dependency:** `uv remove package_name`
- **Show installed packages:** `uv pip list`

## Code Quality & Linting

**Note:** Pre-commit hooks and code quality tools are managed at the repository root level.
See the [main README](../../README.md#code-quality--pre-commit-setup-) for complete setup and usage instructions.

### Project-Specific Linting

You can run linting tools directly on this project:

```bash
# Run all linters manually
make lint                     # Run all linters
make format                   # Format all code
make test                     # Run tests
make ci-check                 # Full CI-style check

# Run specific tools with uv
uv run --group dev black .
uv run --group dev ruff check .
uv run --group dev mypy src/
uv run --group test pytest
```

### Linting Tools Status

- **Ruff**: Fast Python linter ✅ All checks passing
- **Black**: Code formatter ✅ All checks passing
- **isort**: Import sorter ✅ All checks passing
- **MyPy**: Type checker ✅ All checks passing

For detailed linting documentation, see [`docs/LINTING.md`](docs/LINTING.md).

### Project Structure

The project structure design is based on Uncle Bob's Clean Architecture,
for details https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html

```
services/dhagent/
├── .dockerignore         # Docker build context exclusions
├── .gitignore           # Git ignore patterns
├── .python-version       # Python version specification
├── Dockerfile            # Docker image for all environments
├── README.md             # This file
├── main.py              # Main application entry point
├── pyproject.toml       # Project configuration and dependencies
├── uv.lock              # Locked dependencies
├── .venv/               # Virtual environment (created by uv)
└── src/                 # Main source code
    ├── config/          # Configuration management
    ├── entities/        # Entities layer (enterprise business rules)
    ├── usecases/        # Usecases layer (application business rules)
    └── interfaces/      # Interface adapters (API, etc.)
```

### Troubleshooting

- If you encounter permission issues, make sure you have the necessary permissions to create virtual environments
- If `uv` commands fail, ensure you have the latest version: `uv self update`
- For Python version issues, verify that Python 3.12 is installed and accessible

## References

* [Fluent Python](https://li.feishu.cn/wiki/EGnKwC5Gki8fK2kJqJqclV2mnkQ)
* [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
* [Branching Model](https://li.feishu.cn/wiki/DNAlwug7fioGESkCGJmcff3onae)
* [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)
* [git-cz](https://gitlabee.chehejia.com/serverless/git-cz)
