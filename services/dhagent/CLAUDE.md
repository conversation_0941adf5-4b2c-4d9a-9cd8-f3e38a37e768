# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Environment

This is a Python 3.12+ FastAPI application using `uv` as the package manager. The project follows Clean Architecture principles with a focus on SRE (Site Reliability Engineering) operations.

### Essential Commands

**Development Setup:**
```bash
uv sync --group dev --group test    # Install all dependencies
uv run pre-commit install           # Set up git hooks
make dev-setup                      # Complete development setup
```

**Running the Application:**
```bash
uv run python main.py              # Development server
make serve                         # Development with reload
make serve-prod                    # Production server with gunicorn
```

**Code Quality:**
```bash
make lint                          # Run all linters (ruff, mypy, black, isort)
make lint-fix                      # Auto-fix linting issues
make format                        # Format code with black, isort, ruff
make quick-check                   # format + check + test
make ci-check                      # Full CI checks (lint + test)
```

**Testing:**
```bash
make test                          # Run pytest
make test-cov                      # Run tests with coverage
uv run pytest -v                  # Verbose test output
uv run pytest tests/test_file.py  # Run specific test file
```

**Pre-commit Hooks:**
- All commits automatically run formatters and linters
- Tests must pass before commits are allowed
- Use `git commit --no-verify` only in emergencies

## Architecture Overview

This application follows **Clean Architecture** patterns with clear separation of concerns:

### Layer Structure
```
src/
├── entities/          # Core business entities (Alert, OAM models)
├── usecases/         # Business logic and application services
│   ├── alert/        # Alert processing use cases
│   ├── diagnosis/    # Diagnostic capabilities
│   ├── event/        # Event handling (agent and cloud events)
│   └── risk_prediction/ # Risk prediction capabilities
├── interfaces/       # External interfaces and adapters
│   └── api/          # FastAPI REST API layer
└── config/           # Application configuration
```

### Key Components

**Core Entities (`src/entities/`):**
- `Alert`: Core alert entity with severity, status, and SRE metadata
- `OAM`: Operations and monitoring entity models

**Use Cases (`src/usecases/`):**
- **Alert Processing**: Core alert handling and processing logic
- **Event Handling**: Agent events and cloud events processing
- **Diagnosis**: Diagnostic capabilities for SRE operations
- **Risk Prediction**: Predictive analysis for system reliability

**API Layer (`src/interfaces/api/`):**
- FastAPI application with health checks and v1 API routes
- CloudEvents support for event-driven architecture
- Structured logging with thread and transaction IDs

### Dependencies and Integration

**Core Dependencies:**
- `fastapi`: Web framework
- `pydantic`: Data validation and serialization
- `cloudevents`: Event-driven architecture support
- `langchain` + `langgraph`: AI/ML capabilities for SRE automation
- `li_agentcore`: Local package dependency (in `../../packages/li_agentcore`)

**Development Tools:**
- `uv`: Modern Python package manager (not pip/poetry)
- `ruff`: Fast linting and formatting
- `black` + `isort`: Code formatting
- `mypy`: Type checking
- `pytest`: Testing framework
- `pre-commit`: Git hooks for code quality

### Configuration

**Environment Variables:**
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Debug mode (default: false)
- `LOG_LEVEL`: Logging level (default: INFO)

**Key Files:**
- `pyproject.toml`: Project configuration with strict type checking
- `pytest.ini`: Test configuration with markers for unit/integration tests
- `Makefile`: Development workflow commands
- `.pre-commit-config.yaml`: Automated code quality checks

### Development Guidelines

**Code Style:**
- Line length: 88 characters
- Type hints required for all public functions
- Strict mypy checking for `src/`, relaxed for `tests/`
- Import organization: standard library, third-party, first-party

**Testing Strategy:**
- Unit tests for entities and use cases
- Integration tests for API endpoints
- Use pytest markers: `@pytest.mark.unit`, `@pytest.mark.integration`
- Minimum coverage expectations defined in CI

**Clean Architecture Compliance:**
- Entities are framework-independent
- Use cases contain business logic, not framework code
- Interfaces adapt external systems to internal models
- Dependencies point inward (interfaces depend on use cases, not vice versa)

### AI/ML Integration

The application integrates LangChain and LangGraph for SRE automation capabilities. When working with these components:
- Follow the existing event-driven patterns
- Maintain clean separation between AI logic and core SRE entities
- Use structured logging for AI operation traceability
