# Multi-stage build for production FastAPI application
FROM artifactory.chehejia.com/licloud-docker/base/run/python:3.12.2-slim-bookworm-artifactory as builder

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    SOURCE_PATH=services/dhagent \
    LI_AGENTCORE_PATH=packages/li_agentcore

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN pip install uv

RUN mkdir -p /chj/app/${SOURCE_PATH} && chmod 755 -R /chj

# Set work directory
WORKDIR /chj/app

# Copy dependency files
COPY ${SOURCE_PATH}/pyproject.toml ${SOURCE_PATH}/.python-version ${SOURCE_PATH}/uv.lock ${SOURCE_PATH}
COPY ${LI_AGENTCORE_PATH} ${LI_AGENTCORE_PATH}

# Install dependencies
RUN cd ${SOURCE_PATH} && uv sync --frozen

# Production stage
FROM artifactory.chehejia.com/licloud-docker/base/run/python:3.12.2-slim-bookworm-artifactory as production

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    SOURCE_PATH=services/dhagent \
    LI_AGENTCORE_PATH=packages/LI_AGENTCORE \
    PATH="/chj/app/services/dhagent/.venv/bin:$PATH"

# Install system dependencies for runtime
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /chj/app

# Copy virtual environment from builder stage
COPY --from=builder /chj/app/${SOURCE_PATH}/.venv /chj/app/${SOURCE_PATH}/.venv

# Copy application code
COPY ${SOURCE_PATH}/main.py ./${SOURCE_PATH}/
COPY ${SOURCE_PATH}/src ./${SOURCE_PATH}/src
COPY ${LI_AGENTCORE_PATH} ${LI_AGENTCORE_PATH}

# Change ownership to appuser
RUN chown -R appuser:appuser /chj

# Switch to non-root user
USER appuser

# Set working directory to the service directory
WORKDIR /chj/app/${SOURCE_PATH}

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application with Gunicorn + Uvicorn workers for production
CMD ["gunicorn", "src.interfaces.api.app:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--access-logfile", "-", "--error-logfile", "-"]
