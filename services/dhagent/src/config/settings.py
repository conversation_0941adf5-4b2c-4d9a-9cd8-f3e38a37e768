"""Application settings and configuration."""
import json
import os
from typing import Any

from dotenv import load_dotenv

from src.external.licloud.config import ApolloConfigReader, DynamicConfigReader

# Load environment variables from .env file
# Use override=True to ensure .env values take precedence over system environment
load_dotenv(override=True)


class Settings:
    """Application settings."""

    def __init__(self) -> None:
        try:
            self.apollo: DynamicConfigReader | None = ApolloConfigReader()
        except Exception:
            # Apollo not available, fallback to environment variables only
            self.apollo = None

    def _get_apollo_value(self, key: str, config_type: str) -> Any:
        """Get value from Apollo configuration."""
        if not self.apollo:
            return None

        try:
            apollo_methods = {
                "string": self.apollo.get_string,
                "int": self.apollo.get_int,
                "bool": self.apollo.get_bool,
                "float": self.apollo.get_float,
            }
            return apollo_methods[config_type](key)
        except Exception:
            return None

    def _parse_env_value(self, env_value: str, config_type: str, default: Any) -> Any:
        """Parse environment variable value based on type."""
        type_parsers = {
            "int": lambda x: int(x),
            "float": lambda x: float(x),
            "bool": lambda x: x.lower() in ("true", "1", "yes", "on"),
            "string": lambda x: x,
        }

        if config_type in ("int", "float"):
            try:
                return type_parsers[config_type](env_value)
            except ValueError:
                return default

        return type_parsers[config_type](env_value)

    def _get_config(self, key: str, default: Any = "", config_type: str = "string") -> Any:
        """
        Get configuration value with priority: Apollo > Environment Variable > Default.
        Uses the same key for both Apollo and environment variable lookup.

        Args:
            key: Configuration key (used for both Apollo and environment variable)
            default: Default value if neither Apollo nor env var is found
            config_type: Type of config ('string', 'int', 'bool', 'float')

        Returns:
            Configuration value
        """
        # Try Apollo first
        apollo_value = self._get_apollo_value(key, config_type)
        # For booleans, only use True values from Apollo (False falls back to env)
        # For other types, only use non-empty/non-zero values
        if apollo_value is not None and (
            (config_type == "bool" and apollo_value is True) or (config_type != "bool" and apollo_value)
        ):
            return apollo_value

        # Fallback to environment variable
        env_value = os.getenv(key)
        if env_value is not None:
            return self._parse_env_value(env_value, config_type, default)

        return default

    # Application settings
    @property
    def app_name(self) -> str:
        return str(self._get_config("APP_NAME", "dhagent"))

    @property
    def app_version(self) -> str:
        return str(self._get_config("APP_VERSION", "0.0.1"))

    @property
    def app_description(self) -> str:
        return str(self._get_config("APP_DESCRIPTION", "A FastAPI application for AIOps"))

    # Server settings
    @property
    def host(self) -> str:
        return str(self._get_config("HOST", "0.0.0.0"))

    @property
    def port(self) -> int:
        return int(self._get_config("PORT", 8000, "int"))

    @property
    def debug(self) -> bool:
        return bool(self._get_config("DEBUG", False, "bool"))

    # CORS settings
    @property
    def backend_cors_origins(self) -> list[str]:
        # For list configurations, we handle specially
        if self.apollo:
            try:
                # Use the same key for Apollo
                origins = self.apollo.get_string_list("BACKEND_CORS_ORIGINS", is_python_literal=True)
                if origins:
                    return origins
            except Exception:
                pass

        # Fallback to environment variable (comma-separated)
        env_origins = os.getenv("BACKEND_CORS_ORIGINS", "*")
        return [origin.strip() for origin in env_origins.split(",")]

    # Logging settings
    @property
    def log_level(self) -> str:
        return str(self._get_config("LOG_LEVEL", "INFO"))

    # APIHub
    @property
    def apihub_token(self) -> str:
        return str(self._get_config("APIHUB_TOKEN", ""))

    @property
    def langfuse_public_key(self) -> str:
        return str(self._get_config("LANGFUSE_PUBLIC_KEY", ""))

    @property
    def langfuse_secret_key(self) -> str:
        return str(self._get_config("LANGFUSE_SECRET_KEY", ""))

    @property
    def langfuse_host(self) -> str:
        return str(self._get_config("LANGFUSE_HOST", ""))

    @property
    def idaas_host(self) -> str:
        return str(self._get_config("IDAAS_HOST", ""))

    @property
    def idaas_dhagent_client_id(self) -> str:
        return str(self._get_config("IDAAS_DHAGENT_CLIENT_ID", ""))
    
    @property
    def idaas_dhagent_service_id(self) -> str:
        return str(self._get_config("IDAAS_DHAGENT_SERVICE_ID", ""))

    @property
    def idaas_robot_service_id(self) -> str:
        return str(self._get_config("IDAAS_ROBOT_SERVICE_ID", ""))

    @property
    def idaas_robot_service_scopes(self) -> list[str]:
        scopes = str(self._get_config("IDAAS_ROBOT_SERVICE_SCOPES", ""))
        parsed = json.loads(scopes)
        if isinstance(parsed, list):
            return parsed
        return []

    @property
    def robot_api_base_url(self) -> str:
        return str(self._get_config("ROBOT_API_BASE_URL", ""))

    @property
    def robot_api_timeout(self) -> float:
        return float(self._get_config("ROBOT_API_TIMEOUT", 30.0, "float"))


# Global settings instance
settings = Settings()

