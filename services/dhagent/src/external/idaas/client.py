import asyncio
import logging
import threading
from contextlib import asynccontextmanager, contextmanager
from typing import Optional, List, Dict, Any
from functools import wraps
import time

from idaas.app import TokenManager
from src.config import settings

logger = logging.getLogger(__name__)


class IDaaSClientError(Exception):
    """Base exception for IDaaS client errors."""
    pass


class IDaaSClientNotInitializedError(IDaaSClientError):
    """Raised when client is not properly initialized."""
    pass


class IDaaSTokenRetrievalError(IDaaSClientError):
    """Raised when token retrieval fails."""
    pass


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """Decorator for retrying failed operations."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Attempt {attempt + 1} failed: {e}, retrying in {delay}s...")
                        await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                    else:
                        logger.error(f"All {max_retries} attempts failed")
            raise last_exception

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Attempt {attempt + 1} failed: {e}, retrying in {delay}s...")
                        time.sleep(delay * (2 ** attempt))  # Exponential backoff
                    else:
                        logger.error(f"All {max_retries} attempts failed")
            raise last_exception

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


class IDaaSClient:
    """
    Enhanced IDaaS client with automatic initialization, retry mechanism, and context management.

    Features:
    - Thread-safe singleton pattern
    - Automatic initialization from settings
    - Retry mechanism for failed requests
    - Context manager support
    - Comprehensive error handling
    - Token caching (handled by underlying TokenManager)
    """

    _instance: Optional['IDaaSClient'] = None
    _lock = threading.Lock()
    _async_lock = asyncio.Lock()

    def __new__(cls) -> 'IDaaSClient':
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.client_id: Optional[str] = None
        self.client_secret: Optional[str] = None
        self.service_id: Optional[str] = None
        self.host: Optional[str] = None
        self.scopes: List[str] = []
        self._token_manager: Optional[TokenManager] = None
        self._initialized = False

    def initialize(
        self,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        service_id: Optional[str] = None,
        host: Optional[str] = None,
        scopes: Optional[List[str]] = None
    ) -> 'IDaaSClient':
        """
        Initialize IDaaS client. If parameters are not provided, will use settings.

        Args:
            client_id: IDaaS client ID (defaults to settings.idaas_client_id)
            client_secret: IDaaS client secret (defaults to settings.idaas_client_secret)
            service_id: IDaaS service ID (defaults to settings.idaas_robot_service_id)
            host: IDaaS host (defaults to settings.idaas_host)
            scopes: Default scopes (defaults to robot scopes)

        Returns:
            Self for method chaining

        Raises:
            IDaaSClientError: If required parameters are missing
        """
        # Use settings as defaults
        self.client_id = client_id or settings.idaas_client_id
        self.client_secret = client_secret or settings.idaas_client_secret
        self.service_id = service_id or settings.idaas_robot_service_id
        self.host = host or settings.idaas_host
        self.scopes = scopes or ["robot:conversation", "robot:credentials:create"]

        # Validate required parameters
        if not all([self.client_id, self.client_secret, self.service_id, self.host]):
            missing = [
                name for name, value in [
                    ("client_id", self.client_id),
                    ("client_secret", self.client_secret),
                    ("service_id", self.service_id),
                    ("host", self.host)
                ] if not value
            ]
            raise IDaaSClientError(f"Missing required parameters: {', '.join(missing)}")

        try:
            self._token_manager = TokenManager.singleton_m2m(
                self.host,
                self.client_id,
                lambda cli: self.client_secret
            )
            self._initialized = True
            logger.info(f"IDaaS client initialized successfully for service: {self.service_id}")
        except Exception as e:
            raise IDaaSClientError(f"Failed to initialize TokenManager: {e}") from e

        return self

    def _ensure_initialized(self) -> None:
        """Ensure client is initialized, auto-initialize if not."""
        if not self._initialized:
            logger.info("Auto-initializing IDaaS client from settings")
            self.initialize()

    @retry_on_failure(max_retries=3, delay=1.0)
    def get_token(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (synchronous).

        Args:
            scopes: Optional scopes to request (defaults to client's default scopes)

        Returns:
            Access token string

        Raises:
            IDaaSClientNotInitializedError: If client is not initialized
            IDaaSTokenRetrievalError: If token retrieval fails
        """
        self._ensure_initialized()

        if not self._token_manager:
            raise IDaaSClientNotInitializedError("Token manager not available")

        use_scopes = scopes if scopes is not None else self.scopes

        try:
            bundle = asyncio.run(
                self._token_manager.get_token(
                    self.client_id,
                    self.service_id,
                    *use_scopes
                )
            )
            logger.debug(f"Successfully retrieved token for scopes: {use_scopes}")
            return bundle.access_token
        except Exception as e:
            raise IDaaSTokenRetrievalError(f"Failed to retrieve token: {e}") from e

    @retry_on_failure(max_retries=3, delay=1.0)
    async def get_token_async(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (asynchronous).

        Args:
            scopes: Optional scopes to request (defaults to client's default scopes)

        Returns:
            Access token string

        Raises:
            IDaaSClientNotInitializedError: If client is not initialized
            IDaaSTokenRetrievalError: If token retrieval fails
        """
        self._ensure_initialized()

        if not self._token_manager:
            raise IDaaSClientNotInitializedError("Token manager not available")

        use_scopes = scopes if scopes is not None else self.scopes

        try:
            bundle = await self._token_manager.get_token(
                self.client_id,
                self.service_id,
                *use_scopes
            )
            logger.debug(f"Successfully retrieved token for scopes: {use_scopes}")
            return bundle.access_token
        except Exception as e:
            raise IDaaSTokenRetrievalError(f"Failed to retrieve token: {e}") from e

    @contextmanager
    def token_context(self, scopes: Optional[List[str]] = None):
        """
        Context manager for token usage (synchronous).

        Usage:
            with client.token_context() as token:
                # Use token for API calls
                pass
        """
        token = self.get_token(scopes)
        try:
            yield token
        finally:
            # Could add token cleanup logic here if needed
            pass

    @asynccontextmanager
    async def async_token_context(self, scopes: Optional[List[str]] = None):
        """
        Async context manager for token usage.

        Usage:
            async with client.async_token_context() as token:
                # Use token for API calls
                pass
        """
        token = await self.get_token_async(scopes)
        try:
            yield token
        finally:
            # Could add token cleanup logic here if needed
            pass

    def is_initialized(self) -> bool:
        """Check if client is properly initialized."""
        return self._initialized and self._token_manager is not None

    def get_client_info(self) -> Dict[str, Any]:
        """Get client configuration info (excluding sensitive data)."""
        return {
            "client_id": self.client_id,
            "service_id": self.service_id,
            "host": self.host,
            "scopes": self.scopes,
            "initialized": self._initialized
        }

    def __repr__(self) -> str:
        return f"IDaaSClient(service_id={self.service_id}, initialized={self._initialized})"


# Global client instance
client = IDaaSClient()


def get_client() -> IDaaSClient:
    """Get the global IDaaS client instance."""
    return client


if __name__ == '__main__':
    # Example usage
    try:
        # Auto-initialize from settings
        token = client.get_token()
        print(f"Token retrieved: {token[:20]}...")

        # Or explicit initialization
        client.initialize(
            client_id=settings.idaas_client_id,
            client_secret=settings.idaas_client_secret,
            service_id=settings.idaas_robot_service_id,
            host=settings.idaas_host,
            scopes=["robot:conversation", "robot:credentials:create"]
        )

        # Using context manager
        with client.token_context() as token:
            print(f"Token in context: {token[:20]}...")

    except Exception as e:
        logger.error(f"Error: {e}")
        print(f"Error: {e}")