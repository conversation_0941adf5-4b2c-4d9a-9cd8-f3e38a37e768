import asyncio
from typing import Optional, List
from idaas.app import TokenManager
from src.config import settings


class IDaaSClient:
    """
    IDaaS client singleton, supporting synchronous and asynchronous access token retrieval.
    """
    _instance: Optional['IDaaSClient'] = None
    _lock = asyncio.Lock()

    def __init__(self):
        self.scopes = None
        self._token_manager = None
        self.host = None
        self.service_id = None
        self.client_secret = None
        self.client_id = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def initialize(
        self,
        client_id: str,
        client_secret: str,
        service_id: str,
        host: str,
        scopes: Optional[List[str]] = None
    ) -> None:
        """
        Initialize IDaaS client singleton instance.
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.service_id = service_id
        self.host = host
        self.scopes = scopes or []
        self._token_manager = TokenManager.singleton_m2m(
            host,
            client_id,
            lambda cli: client_secret
        )

    def get_token(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (sync).
        """
        if not hasattr(self, '_token_manager') or self._token_manager is None:
            raise RuntimeError("IDaaS client not initialized, please invoke initialize() first.")
        use_scopes = scopes if scopes is not None else self.scopes or []
        bundle = asyncio.run(
            self._token_manager.get_token(
                self.client_id,
                self.service_id,
                *use_scopes
            )
        )
        return bundle.access_token

    async def get_token_async(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (async).
        """
        if not hasattr(self, '_token_manager') or self._token_manager is None:
            raise RuntimeError("IDaaS client not initialized, please invoke initialize() first.")
        use_scopes = scopes if scopes is not None else self.scopes or []
        bundle = await self._token_manager.get_token(
            self.client_id,
            self.service_id,
            *use_scopes
        )
        return bundle.access_token


client = IDaaSClient()

if __name__ == '__main__':
    client.initialize(
        client_id=settings.idaas_client_id,
        client_secret=settings.idaas_client_secret,
        service_id=settings.idaas_robot_service_id,
        host=settings.idaas_host,
        scopes=["robot:conversation", "robot:credentials:create"]
    )
    token = client.get_token()
    print(token)