import asyncio
from typing import Optional, List
from idaas.app import Token<PERSON>anager
from src.config import settings


class IDaaSClient:
    _instance: Optional['IDaaSClient'] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        self.client_id: Optional[str] = None
        self.client_secret: Optional[str] = None
        self.service_id: Optional[str] = None
        self.endpoint: Optional[str] = None
        self.scopes: List[str] = []
        self._token_manager: Optional[TokenManager] = None

    def initialize(
        self,
        client_id: str,
        client_secret: str,
        service_id: str,
        endpoint: str,
        scopes: Optional[List[str]] = None
    ) -> None:
        """
        Initialize the IDaaS client

        Args:
            client_id: App ID created in the IDaaS console
            client_secret: App Secret for the server application
            service_id: Service ID created in the IDaaS console
            endpoint: The address of the IDaaS credential service, ending with /api
            scopes: Permissions required in the credential
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.service_id = service_id
        self.endpoint = endpoint
        self.scopes = scopes or []

        # 初始化 TokenManager，保证单例
        self._token_manager = TokenManager.singleton_m2m(
            endpoint,
            client_id,
            lambda cli: client_secret
        )

    def get_token(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (synchronous)

        Args:
            scopes: Optional permission scopes, use default if not provided

        Returns:
            Access token string
        """
        if not self._token_manager:
            raise RuntimeError("IDaaS client not initialized, please call initialize() first.")

        use_scopes = scopes if scopes is not None else self.scopes
        bundle = asyncio.run(
            self._token_manager.get_token(
                self.client_id,
                self.service_id,
                *use_scopes
            )
        )
        return bundle.access_token

    async def get_token_async(self, scopes: Optional[List[str]] = None) -> str:
        """
        Get access token (asynchronous)

        Args:
            scopes: Optional permission scopes, use default if not provided

        Returns:
            Access token string
        """
        if not self._token_manager:
            raise RuntimeError("IDaaS client not initialized, please call initialize() first.")

        use_scopes = scopes if scopes is not None else self.scopes
        bundle = await self._token_manager.get_token(
            self.client_id,
            self.service_id,
            *use_scopes
        )
        return bundle.access_token

