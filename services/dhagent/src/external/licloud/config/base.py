from abc import ABC, abstractmethod
from typing import Any, TypeVar

from pydantic import BaseModel

T = TypeVar("T", bound=BaseModel)


class DynamicConfigReader(ABC):
    @abstractmethod
    def get_string_list(self, key: str, is_python_literal: bool = True) -> list[str]:
        """
        Get a list of strings from <PERSON>.

        - ['a', 'b', 'c'] -> is_python_literal=True
        - 'a,b,c'         -> is_python_literal=False
        """
        ...

    @abstractmethod
    def get_pydantic_model_list(self, key: str, model_type: type[T]) -> list[T]: ...

    @abstractmethod
    def get_string(self, key: str) -> str: ...

    @abstractmethod
    def get_json(self, key: str) -> dict[str, Any]: ...

    @abstractmethod
    def get_bool(self, key: str) -> bool: ...

    @abstractmethod
    def get_float(self, key: str) -> float: ...

    @abstractmethod
    def get_int(self, key: str) -> int: ...
