import ast
import json
import logging
import os
from typing import Any

from apollo.apollo_client import ApolloClient
from pydantic import ValidationError

from .base import DynamicConfigReader, T

logger = logging.getLogger(__name__)

SENSITIVE_WORDS = ["KEY", "TOKEN", "SECRET", "PASSWD"]


class ApolloConfigReader(DynamicConfigReader):
    def __init__(self) -> None:
        apollo_config_server_url = os.environ.get("APOLLO_CONFIG_SERVER_URL")

        assert bool(apollo_config_server_url), "APOLLO_CONFIG_SERVER_URL is required"

        self._apollo = ApolloClient(
            app_id="dhagent",
            config_url=apollo_config_server_url,
        )

    def get_string_list(self, key: str, is_python_literal: bool = True) -> list[str]:
        string_list_value = self._apollo.get_value(key)

        value = []

        if string_list_value:
            if is_python_literal:
                value = ast.literal_eval(string_list_value)
            else:
                value = string_list_value.split(",")

        self._log(key, value)

        return value

    def get_pydantic_model_list(self, key: str, model_type: type[T]) -> list[T]:
        string_list_value = self._apollo.get_value(key)
        self._log(key, string_list_value)

        value = []

        if string_list_value:
            try:
                dict_list = ast.literal_eval(string_list_value)
                if isinstance(dict_list, list) and all(isinstance(item, dict) for item in dict_list):
                    value = [model_type(**item) for item in dict_list]
                else:
                    logger.warning(f"Invalid format: Expected a list of dictionaries. key={key}")
                    return value
            except ValidationError as e:
                logger.warning(f"Pydantic validation error, error={e}")
                return value
            except (ValueError, SyntaxError) as e:
                logger.warning(f"Literal eval error, error={e}")
                return value

        return value

    def get_string(self, key: str) -> str:
        value = self._apollo.get_value(key)
        self._log(key, value)

        if not value:
            return ""
        return str(value)

    def get_json(self, key: str) -> dict[str, Any]:
        value = self._apollo.get_value(key)
        self._log(key, value)

        if not value:
            return {}

        parsed_value: dict[str, Any] = json.loads(value)
        return parsed_value

    def get_bool(self, key: str) -> bool:
        value = self._apollo.get_value(key)
        self._log(key, value)

        if value:
            value = value.lower() in ["true", "1", "yes", "on"]
        return bool(value)

    def get_float(self, key: str) -> float:
        value = self._apollo.get_value(key)
        self._log(key, value)

        if not value:
            return 0.0

        try:
            value = float(value)
        except ValueError:
            value = 0.0

        return value

    def get_int(self, key: str) -> int:
        value = self._apollo.get_value(key)
        self._log(key, value)

        if not value:
            return 0

        try:
            value = int(value)
        except ValueError:
            value = 0

        return value

    @staticmethod
    def _log(key: str, value: Any) -> None:
        if any(word in key for word in SENSITIVE_WORDS):
            logger.debug(f"key={key}, raw_value=***")
        else:
            logger.debug(f"key={key}, raw_value={value}")
