"""
Robot HTTP client with automatic IDaaS token management.

This module provides a convenient HTTP client specifically designed for robot API interactions,
with automatic token retrieval and injection.
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Union
from contextlib import asynccontextmanager

import httpx
from httpx import Response

from src.external.idaas.client import get_client as get_idaas_client, IDaaSClientError
from .config.idaas_config import ROBOT_IDAAS_SCOPES

logger = logging.getLogger(__name__)


class RobotClientError(Exception):
    """Base exception for robot client errors."""
    pass


class RobotAPIError(RobotClientError):
    """Raised when robot API returns an error."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class RobotClient:
    """
    HTTP client for robot API with automatic IDaaS token management.
    
    Features:
    - Automatic token retrieval and injection
    - Async and sync support
    - Comprehensive error handling
    - Request/response logging
    - Configurable timeouts and retries
    """
    
    def __init__(
        self,
        base_url: Optional[str] = None,
        timeout: float = 30.0,
        scopes: Optional[list[str]] = None
    ):
        """
        Initialize robot client.
        
        Args:
            base_url: Base URL for robot API (if None, must be provided per request)
            timeout: Request timeout in seconds
            scopes: IDaaS scopes to request (defaults to robot scopes)
        """
        self.base_url = base_url
        self.timeout = timeout
        self.scopes = scopes or ROBOT_IDAAS_SCOPES
        self._idaas_client = get_idaas_client()

    async def _get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers with fresh token."""
        try:
            token = await self._idaas_client.get_token_async(self.scopes)
            return {"Authorization": f"Bearer {token}"}
        except Exception as e:
            raise RobotClientError(f"Failed to get authentication token: {e}") from e

    def _get_auth_headers_sync(self) -> Dict[str, str]:
        """Get authorization headers with fresh token (synchronous)."""
        try:
            token = self._idaas_client.get_token(self.scopes)
            return {"Authorization": f"Bearer {token}"}
        except Exception as e:
            raise RobotClientError(f"Failed to get authentication token: {e}") from e

    def _build_url(self, endpoint: str, base_url: Optional[str] = None) -> str:
        """Build full URL from endpoint."""
        url_base = base_url or self.base_url
        if not url_base:
            raise RobotClientError("Base URL must be provided either in constructor or per request")
        
        return f"{url_base.rstrip('/')}/{endpoint.lstrip('/')}"

    def _handle_response(self, response: Response) -> Dict[str, Any]:
        """Handle HTTP response and extract data."""
        try:
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            try:
                error_data = response.json()
            except Exception:
                error_data = {"detail": response.text}
            
            raise RobotAPIError(
                f"Robot API error: {e.response.status_code} {e.response.reason_phrase}",
                status_code=e.response.status_code,
                response_data=error_data
            ) from e
        except Exception as e:
            raise RobotClientError(f"Failed to process response: {e}") from e

    async def request(
        self,
        method: str,
        endpoint: str,
        *,
        base_url: Optional[str] = None,
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Make an authenticated HTTP request to robot API.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            endpoint: API endpoint (relative to base_url)
            base_url: Override base URL for this request
            json_data: JSON data to send in request body
            params: Query parameters
            headers: Additional headers (auth headers will be added automatically)
            timeout: Request timeout (overrides default)
            
        Returns:
            Response data as dictionary
            
        Raises:
            RobotClientError: For client-side errors
            RobotAPIError: For API errors
        """
        url = self._build_url(endpoint, base_url)
        auth_headers = await self._get_auth_headers()
        
        # Merge headers
        request_headers = {**auth_headers}
        if headers:
            request_headers.update(headers)
        
        request_timeout = timeout or self.timeout
        
        logger.debug(f"Making {method} request to {url}")
        
        async with httpx.AsyncClient(timeout=request_timeout) as client:
            response = await client.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                headers=request_headers
            )
            
        return self._handle_response(response)

    def request_sync(
        self,
        method: str,
        endpoint: str,
        *,
        base_url: Optional[str] = None,
        json_data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Make an authenticated HTTP request to robot API (synchronous).
        
        Same as request() but synchronous.
        """
        url = self._build_url(endpoint, base_url)
        auth_headers = self._get_auth_headers_sync()
        
        # Merge headers
        request_headers = {**auth_headers}
        if headers:
            request_headers.update(headers)
        
        request_timeout = timeout or self.timeout
        
        logger.debug(f"Making {method} request to {url}")
        
        with httpx.Client(timeout=request_timeout) as client:
            response = client.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                headers=request_headers
            )
            
        return self._handle_response(response)

    # Convenience methods for common HTTP verbs
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET request."""
        return await self.request("GET", endpoint, **kwargs)

    async def post(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """POST request."""
        return await self.request("POST", endpoint, **kwargs)

    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """PUT request."""
        return await self.request("PUT", endpoint, **kwargs)

    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE request."""
        return await self.request("DELETE", endpoint, **kwargs)

    # Synchronous convenience methods
    def get_sync(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET request (sync)."""
        return self.request_sync("GET", endpoint, **kwargs)

    def post_sync(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """POST request (sync)."""
        return self.request_sync("POST", endpoint, **kwargs)

    def put_sync(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """PUT request (sync)."""
        return self.request_sync("PUT", endpoint, **kwargs)

    def delete_sync(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE request (sync)."""
        return self.request_sync("DELETE", endpoint, **kwargs)

    @asynccontextmanager
    async def session(self):
        """
        Context manager for reusing HTTP client session.
        
        Usage:
            async with robot_client.session() as session:
                # Multiple requests with same session
                pass
        """
        auth_headers = await self._get_auth_headers()
        
        async with httpx.AsyncClient(
            timeout=self.timeout,
            headers=auth_headers
        ) as client:
            yield RobotSession(client, self)


class RobotSession:
    """Session wrapper for making multiple requests with same auth token."""
    
    def __init__(self, client: httpx.AsyncClient, robot_client: RobotClient):
        self.client = client
        self.robot_client = robot_client

    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make request using session client."""
        url = self.robot_client._build_url(endpoint, kwargs.pop('base_url', None))
        response = await self.client.request(method, url, **kwargs)
        return self.robot_client._handle_response(response)

    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        return await self.request("GET", endpoint, **kwargs)

    async def post(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        return await self.request("POST", endpoint, **kwargs)

    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        return await self.request("PUT", endpoint, **kwargs)

    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        return await self.request("DELETE", endpoint, **kwargs)


# Global client instance
default_client = RobotClient()


def get_client() -> RobotClient:
    """Get the default robot client instance."""
    return default_client
