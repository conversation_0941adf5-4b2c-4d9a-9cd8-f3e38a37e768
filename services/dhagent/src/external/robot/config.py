"""Robot configuration settings."""

from src.config.settings import settings

# Robot IDaaS configuration
ROBOT_IDAAS_HOST = settings.idaas_host
ROBOT_IDAAS_CLIENT_ID = settings.idaas_dhagent_client_id
ROBOT_IDAAS_CLIENT_SECRET = settings.idaas_dhagent_service_id
ROBOT_IDAAS_SERVICE_ID = settings.idaas_robot_service_id
ROBOT_IDAAS_SCOPES = settings.idaas_robot_service_scopes

# Robot API configuration
ROBOT_API_BASE_URL = settings.robot_api_base_url
ROBOT_API_TIMEOUT = settings.robot_api_timeout

# All parameters are loaded from global settings for consistency.