"""Robot service factory - Dependency injection and configuration."""

import logging

from src.external.idaas.client import IDaa<PERSON>lient
from src.usecases.robot.robot_service import RobotService

from .config import (
    ROBOT_API_BASE_URL,
    ROBOT_API_TIMEOUT,
    ROBOT_IDAAS_CLIENT_ID,
    ROBOT_IDAAS_CLIENT_SECRET,
    ROBOT_IDAAS_HOST,
    ROBOT_IDAAS_SCOPES,
    ROBOT_IDAAS_SERVICE_ID,
)
from .http_client import RobotHttpClient

logger = logging.getLogger(__name__)


class RobotServiceFactory:
    """
    Factory for creating robot service instances with proper dependency injection.

    This factory handles the creation and configuration of robot services,
    ensuring proper initialization of dependencies and following Clean Architecture
    principles by managing the dependency graph.
    """

    _robot_service: RobotService | None = None
    _default_robot_service: RobotService | None = None

    @classmethod
    def create_robot_service(
            cls,
            base_url: str | None = None,
            timeout: float | None = None,
            force_recreate: bool = False
    ) -> RobotService:
        """
        Create or return cached robot service instance.

        Args:
            base_url: Override default robot API base URL
            timeout: Override default request timeout
            force_recreate: Force creation of new instance

        Returns:
            Configured robot service instance
        """
        if cls._robot_service is None or force_recreate:
            cls._robot_service = cls._build_robot_service(base_url, timeout)

        return cls._robot_service

    @classmethod
    def _build_robot_service(
            cls,
            base_url: str | None = None,
            timeout: float | None = None
    ) -> RobotService:
        """
        Build robot service with all dependencies.

        Args:
            base_url: Robot API base URL
            timeout: Request timeout

        Returns:
            Configured robot service instance
        """
        # Create and configure IDaaS client
        idaas_client = IDaaSClient()
        idaas_client.initialize(
            client_id=ROBOT_IDAAS_CLIENT_ID,
            client_secret=ROBOT_IDAAS_CLIENT_SECRET,
            service_id=ROBOT_IDAAS_SERVICE_ID,
            endpoint=ROBOT_IDAAS_HOST,
            scopes=ROBOT_IDAAS_SCOPES
        )

        # Create HTTP client (repository implementation)
        api_base_url = base_url or ROBOT_API_BASE_URL
        api_timeout = timeout or ROBOT_API_TIMEOUT

        robot_repository = RobotHttpClient(
            base_url=api_base_url,
            idaas_client=idaas_client,
            timeout=api_timeout
        )

        # Create and return service
        robot_service = RobotService(robot_repository)

        logger.info(f"Created robot service with base URL: {api_base_url}")
        return robot_service

    @classmethod
    def get_default_robot_service(cls) -> RobotService:
        """
        Get or create the default robot service instance.

        Returns:
            Default robot service instance
        """
        if cls._default_robot_service is None:
            cls._default_robot_service = cls.create_robot_service()
        return cls._default_robot_service

    @classmethod
    def reset(cls) -> None:
        """Reset factory state (useful for testing)."""
        cls._robot_service = None
        cls._default_robot_service = None


def get_robot_service() -> RobotService:
    """
    Get the default robot service instance.

    Returns:
        Default robot service instance
    """
    return RobotServiceFactory.get_default_robot_service()


def create_robot_service(
        base_url: str | None = None,
        timeout: float | None = None
) -> RobotService:
    """
    Create a new robot service instance with custom configuration.
    
    Args:
        base_url: Custom robot API base URL
        timeout: Custom request timeout
        
    Returns:
        New robot service instance
    """
    return RobotServiceFactory.create_robot_service(
        base_url=base_url,
        timeout=timeout,
        force_recreate=True
    )
