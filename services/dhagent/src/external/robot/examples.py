"""
Robot service usage examples demonstrating Clean Architecture implementation.

This module shows how to use the robot service for various operations,
following the established patterns and best practices.
"""

import asyncio
import logging
from typing import Dict, Any

from src.entities.robot import MemberIdType, SearchType
from src.usecases.robot.repositories import RobotRepositoryError
from src.usecases.robot.robot_service import RobotServiceError
from src.external.robot import get_robot_service

logger = logging.getLogger(__name__)

async def example_get_user_authorization():
    """Example: Get user authorization tokens."""
    robot_service = get_robot_service()
    
    try:
        # Get authorization for multiple services
        response = await robot_service.get_user_authorization(
            user_id="jingxuyang",
            service_ids_with_scopes={
                "1TmoBZo2H9TXGvdOkHDc": ["create_ticket:read"],
                "1TmoBZo2H9TXGvdOkH12": ["ticket:write"]
            },
            search_type=SearchType.LDAP_NAME
        )
        
        print(f"Authorization response code: {response.code}")
        print(f"Authorization message: {response.msg}")
        
        for service_token in response.data["services"]:
            if service_token.error:
                print(f"Service {service_token.id}: Error - {service_token.error}")
            else:
                print(f"Service {service_token.id}: Token expires in {service_token.expires_in}s")
                
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_create_conversation():
    """Example: Create a new conversation."""
    robot_service = get_robot_service()
    
    try:
        response = await robot_service.create_conversation(
            title="helper发布诊断",
            member_ids=["zhouzheng5"],
            scenario="deepheal",
            member_id_type=MemberIdType.LDAP_NAME
        )
        
        conversation = response.data
        print(f"Created conversation {conversation.id}: '{conversation.title}'")
        print(f"Chat ID: {conversation.chat_id}")
        print(f"Assistant: {conversation.primary_assistant_display_name}")
        
        return conversation.id
        
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return None



async def example_invite_users(conversation_id: int):
    """Example: Invite users to conversation."""
    robot_service = get_robot_service()
    
    try:
        response = await robot_service.invite_users_to_conversation(
            conversation_id=conversation_id,
            member_ids=["zhenghe", "lisongnan"],
            member_id_type=MemberIdType.LDAP_NAME
        )
        
        print(f"Invitation response code: {response.code}")
        invalid_ids = response.data.get("invalid_id_list", [])
        if invalid_ids:
            print(f"Invalid member IDs: {invalid_ids}")
        else:
            print("All members invited successfully")
            
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_update_conversation_title(conversation_id: int):
    """Example: Update conversation title."""
    robot_service = get_robot_service()
    
    try:
        response = await robot_service.update_conversation_title(
            conversation_id=conversation_id,
            title="Updated Title - DeepHeal Diagnosis"
        )
        
        conversation = response.data
        print(f"Updated conversation {conversation.id} title to: '{conversation.title}'")
        
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_send_message(conversation_id: int):
    """Example: Send a message to conversation."""
    robot_service = get_robot_service()
    
    try:
        # Example message with Feishu card template
        messages = [
            {
                "sender": {
                    "role": "agent",
                    "name": "dhagent"
                },
                "recipient": {
                    "role": "notifier",
                    "name": ""
                },
                "recipient_scope": "single_group",
                "contents": [
                    {
                        "mime_type": "application/vnd.feishu.card.template.v2+json",
                        "body": {
                            "type": "template",
                            "data": {
                                "template_id": "AAqdzUE2z9RVt",
                                "template_version_name": "1.0.0",
                                "template_variable": {
                                    "component_name": "op-robot-cloud-helper-service"
                                }
                            }
                        }
                    }
                ],
                "metadata": {}
            }
        ]
        
        response = await robot_service.send_conversation_message(
            conversation_id=conversation_id,
            messages=messages
        )
        
        print(f"Message sent response code: {response.code}")
        print(f"Message sent response: {response.msg}")
        
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_get_conversation_history(conversation_id: int):
    """Example: Get conversation history."""
    robot_service = get_robot_service()
    
    try:
        response = await robot_service.get_conversation_history(conversation_id)
        
        history = response.data
        print(f"Conversation {history.id}: '{history.title}'")
        print(f"Created: {history.create_time}")
        print(f"Nodes in history: {len(history.nodes)}")
        
        for node in history.nodes[:3]:  # Show first 3 nodes
            message = node.message
            print(f"  Message {message.id}: {message.sender.role} -> {message.recipient.role}")
            print(f"    Status: {message.status}, End turn: {message.end_turn}")
            
    except RobotServiceError as e:
        logger.error(f"Service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_complete_workflow():
    """Example: Complete workflow demonstrating all operations."""
    print("=== Robot Service Complete Workflow Example ===\n")
    
    # 1. Get user authorization
    print("1. Getting user authorization...")
    await example_get_user_authorization()
    print()
    
    # 2. Create conversation
    print("2. Creating conversation...")
    conversation_id = await example_create_conversation()
    if not conversation_id:
        print("Failed to create conversation, stopping workflow")
        return
    print()
    
    # 3. Invite users
    print("3. Inviting users to conversation...")
    await example_invite_users(conversation_id)
    print()
    
    # 4. Update conversation title
    print("4. Updating conversation title...")
    await example_update_conversation_title(conversation_id)
    print()
    
    # 5. Send message
    print("5. Sending message...")
    await example_send_message(conversation_id)
    print()
    
    # 6. Get conversation history
    print("6. Getting conversation history...")
    await example_get_conversation_history(conversation_id)
    print()
    
    print("=== Workflow completed ===")


async def example_error_handling():
    """Example: Error handling patterns."""
    robot_service = get_robot_service()
    
    try:
        # This should fail with validation error
        await robot_service.get_user_authorization(
            user_id="",  # Empty user ID
            service_ids_with_scopes={}  # Empty services
        )
    except RobotServiceError as e:
        print(f"Caught service error (expected): {e}")
    
    try:
        # This should fail with not found error
        await robot_service.get_conversation_history(999999)
    except RobotServiceError as e:
        print(f"Caught service error for non-existent conversation: {e}")


if __name__ == "__main__":
    asyncio.run(example_create_conversation())