"""
Usage examples for the Robot client with IDaaS integration.

This module demonstrates various ways to use the robot client for API interactions.
"""

import asyncio
import logging
from typing import Dict, Any

from .client import Robot<PERSON>lient, get_client, RobotClientError, RobotAPIError

logger = logging.getLogger(__name__)


# Example robot API endpoints (replace with actual endpoints)
ROBOT_BASE_URL = "https://api.robot.example.com"


async def example_basic_usage():
    """Basic usage example with the default client."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        # Simple GET request
        user_info = await client.get("/api/v1/user/profile")
        print(f"User info: {user_info}")
        
        # POST request with data
        conversation_data = {
            "title": "New Conversation",
            "scenario": "support",
            "member_ids": ["user123", "user456"]
        }
        conversation = await client.post("/api/v1/conversations", json_data=conversation_data)
        print(f"Created conversation: {conversation}")
        
    except RobotAPIError as e:
        logger.error(f"Robot API error: {e}, status: {e.status_code}")
    except RobotClientError as e:
        logger.error(f"Client error: {e}")


async def example_custom_client():
    """Example using a custom client instance."""
    client = RobotClient(
        base_url=ROBOT_BASE_URL,
        timeout=60.0,  # Custom timeout
        scopes=["robot:conversation", "robot:admin"]  # Custom scopes
    )
    
    try:
        # Get conversations
        conversations = await client.get("/api/v1/conversations")
        print(f"Found {len(conversations.get('data', []))} conversations")
        
        # Update conversation
        conversation_id = "conv_123"
        update_data = {"title": "Updated Title"}
        updated = await client.put(f"/api/v1/conversations/{conversation_id}", json_data=update_data)
        print(f"Updated conversation: {updated}")
        
    except Exception as e:
        logger.error(f"Error: {e}")


async def example_session_usage():
    """Example using session for multiple requests."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        async with client.session() as session:
            # Multiple requests with same auth token
            user = await session.get("/api/v1/user/profile")
            conversations = await session.get("/api/v1/conversations")
            
            # Create new conversation
            new_conv = await session.post("/api/v1/conversations", json={
                "title": "Session Example",
                "scenario": "demo"
            })
            
            print(f"User: {user.get('name')}")
            print(f"Conversations: {len(conversations.get('data', []))}")
            print(f"New conversation ID: {new_conv.get('id')}")
            
    except Exception as e:
        logger.error(f"Session error: {e}")


def example_sync_usage():
    """Example using synchronous methods."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        # Synchronous requests
        user_info = client.get_sync("/api/v1/user/profile")
        print(f"User (sync): {user_info}")
        
        # Create conversation synchronously
        conversation = client.post_sync("/api/v1/conversations", json_data={
            "title": "Sync Conversation",
            "scenario": "sync_demo"
        })
        print(f"Created conversation (sync): {conversation}")
        
    except Exception as e:
        logger.error(f"Sync error: {e}")


async def example_error_handling():
    """Example demonstrating comprehensive error handling."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        # This might fail with 404
        result = await client.get("/api/v1/nonexistent")
        
    except RobotAPIError as e:
        if e.status_code == 404:
            logger.info("Resource not found, this is expected")
        elif e.status_code == 401:
            logger.error("Authentication failed - check IDaaS configuration")
        elif e.status_code == 403:
            logger.error("Access denied - check scopes")
        else:
            logger.error(f"API error: {e.status_code} - {e}")
            
    except RobotClientError as e:
        logger.error(f"Client configuration error: {e}")
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")


async def example_robot_operations():
    """Example of typical robot operations."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        # 1. Get user authorization
        auth_result = await client.post("/api/v1/auth/user", json_data={
            "search_type": "user_id",
            "user_id": "user123",
            "service_ids": ["service1", "service2"]
        })
        print(f"User authorization: {auth_result}")
        
        # 2. Create a conversation group
        group_result = await client.post("/api/v1/groups", json_data={
            "title": "Support Session",
            "scenario": "customer_support",
            "member_id_type": "user_id",
            "member_ids": ["user123", "agent456"]
        })
        conversation_id = group_result.get("conversation_id")
        print(f"Created group: {conversation_id}")
        
        # 3. Invite additional users
        if conversation_id:
            invite_result = await client.post(f"/api/v1/groups/{conversation_id}/invite", json_data={
                "member_id_type": "user_id",
                "member_ids": ["supervisor789"]
            })
            print(f"Invite result: {invite_result}")
        
        # 4. Send a message
        if conversation_id:
            message_result = await client.post(f"/api/v1/conversations/{conversation_id}/messages", json_data={
                "content": "Hello! How can I help you today?",
                "message_type": "text"
            })
            print(f"Message sent: {message_result}")
            
    except Exception as e:
        logger.error(f"Robot operations error: {e}")


async def example_with_custom_headers():
    """Example with custom headers and parameters."""
    client = get_client()
    client.base_url = ROBOT_BASE_URL
    
    try:
        # Request with custom headers and query parameters
        result = await client.get(
            "/api/v1/conversations",
            params={"limit": 10, "status": "active"},
            headers={"X-Request-ID": "req_123", "X-Client-Version": "1.0.0"}
        )
        print(f"Conversations with custom params: {result}")
        
    except Exception as e:
        logger.error(f"Custom headers error: {e}")


async def main():
    """Run all examples."""
    print("=== Robot Client Examples ===\n")
    
    examples = [
        ("Basic Usage", example_basic_usage),
        ("Custom Client", example_custom_client),
        ("Session Usage", example_session_usage),
        ("Error Handling", example_error_handling),
        ("Robot Operations", example_robot_operations),
        ("Custom Headers", example_with_custom_headers),
    ]
    
    for name, example_func in examples:
        print(f"--- {name} ---")
        try:
            if asyncio.iscoroutinefunction(example_func):
                await example_func()
            else:
                example_func()
        except Exception as e:
            print(f"Example failed: {e}")
        print()
    
    print("--- Sync Usage ---")
    try:
        example_sync_usage()
    except Exception as e:
        print(f"Sync example failed: {e}")


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Run examples
    asyncio.run(main())
