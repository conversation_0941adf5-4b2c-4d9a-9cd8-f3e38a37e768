# Robot-related IDaaS configuration
from src.config.settings import settings

ROBOT_IDAAS_CLIENT_ID = settings.idaas_client_id
ROBOT_IDAAS_CLIENT_SECRET = settings.idaas_client_secret
ROBOT_IDAAS_SERVICE_ID = settings.idaas_robot_service_id
ROBOT_IDAAS_HOST = settings.idaas_host
ROBOT_IDAAS_SCOPES = ["robot:conversation", "robot:credentials:create"]

# All parameters are loaded from global settings for consistency.
