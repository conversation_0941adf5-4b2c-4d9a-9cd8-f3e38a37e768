# Robot Client - Clean Architecture Implementation

This package provides a clean, well-structured interface for robot operations following Clean Architecture principles.

## Architecture Overview

The implementation follows Clean Architecture with clear separation of concerns:

```
src/external/robot/
├── entities/robot.py          # Domain entities (Business objects)
├── usecases/robot/           # Use cases (Business logic)
│   ├── repositories.py       # Repository interfaces
│   └── robot_service.py      # Business logic implementation
└── external/robot/           # Infrastructure layer
    ├── http_client.py        # HTTP client implementation
    ├── factory.py            # Dependency injection
    ├── config.py             # Configuration
    └── examples.py           # Usage examples
```

## Key Features

- **Clean Architecture**: Clear separation between domain, use cases, and infrastructure
- **Dependency Injection**: Proper IoC with factory pattern
- **Type Safety**: Full Pydantic models for request/response validation
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Async Support**: Full async/await support throughout
- **IDaaS Integration**: Automatic token management with retry logic
- **Logging**: Structured logging for observability

## Quick Start

### Basic Usage

```python
from src.external.robot import get_robot_service

# Get the default robot service
robot_service = get_robot_service()

# Get user authorization
response = await robot_service.get_user_authorization(
    user_id="jingxuyang",
    service_ids_with_scopes={
        "service1": ["read", "write"],
        "service2": ["admin"]
    }
)

# Create conversation
conversation = await robot_service.create_conversation(
    title="Support Session",
    member_ids=["user1", "user2"]
)
```

### Custom Configuration

```python
from src.external.robot import create_robot_service

# Create service with custom configuration
robot_service = create_robot_service(
    base_url="https://custom-robot-api.com",
    timeout=60.0
)
```

## API Reference

### RobotService Methods

#### get_user_authorization()
Get user authorization tokens for specified services.

```python
response = await robot_service.get_user_authorization(
    user_id="jingxuyang",
    service_ids_with_scopes={
        "1TmoBZo2H9TXGvdOkHDc": ["create_ticket:read"],
        "1TmoBZo2H9TXGvdOkH12": ["ticket:write"]
    },
    search_type=SearchType.LDAP_NAME,
    said_token="optional_said_token"
)
```

#### create_conversation()
Create a new robot conversation.

```python
conversation = await robot_service.create_conversation(
    title="helper发布诊断-[井旭阳]",
    member_ids=["jingxuyang"],
    scenario="deepheal",
    member_id_type=MemberIdType.LDAP_NAME
)
```

#### invite_users_to_conversation()
Invite users to join an existing conversation.

```python
response = await robot_service.invite_users_to_conversation(
    conversation_id=5235,
    member_ids=["zhenghe", "lisongnan"],
    member_id_type=MemberIdType.LDAP_NAME
)
```

#### update_conversation_title()
Update conversation title.

```python
response = await robot_service.update_conversation_title(
    conversation_id=5235,
    title="Updated Title"
)
```

#### send_conversation_message()
Send a message to a conversation.

```python
messages = [{
    "sender": {"role": "agent", "name": "dhagent"},
    "recipient": {"role": "notifier", "name": ""},
    "recipient_scope": "single_group",
    "contents": [{"mime_type": "text/plain", "body": {"text": "Hello"}}],
    "metadata": {}
}]

response = await robot_service.send_conversation_message(
    conversation_id=5235,
    messages=messages
)
```

#### get_conversation_history()
Get conversation history.

```python
history = await robot_service.get_conversation_history(conversation_id=5235)
```

## Error Handling

The client provides comprehensive error handling with specific exception types:

```python
from src.usecases.robot.repositories import (
    RobotAuthenticationError,
    RobotNotFoundError,
    RobotValidationError,
    RobotNetworkError
)
from src.usecases.robot.robot_service import RobotServiceError

try:
    response = await robot_service.get_user_authorization(...)
except RobotAuthenticationError:
    # Handle authentication failures
    pass
except RobotNotFoundError:
    # Handle resource not found
    pass
except RobotValidationError:
    # Handle validation errors
    pass
except RobotNetworkError:
    # Handle network issues
    pass
except RobotServiceError as e:
    # Handle general service errors
    logger.error(f"Service error: {e}")
```

## Configuration

Configuration is managed through the settings system:

```python
# In src/external/robot/config.py
ROBOT_IDAAS_CLIENT_ID = settings.idaas_client_id
ROBOT_IDAAS_CLIENT_SECRET = settings.idaas_client_secret
ROBOT_IDAAS_SERVICE_ID = settings.idaas_robot_service_id
ROBOT_IDAAS_HOST = settings.idaas_host
ROBOT_IDAAS_SCOPES = ["robot:conversation", "robot:credentials:create"]

ROBOT_API_BASE_URL = "https://robot-api.example.com"
ROBOT_API_TIMEOUT = 30.0
```

## Testing

Run the examples to test the implementation:

```python
# Run all examples
python -m src.external.robot.examples

# Or import and run specific examples
from src.external.robot.examples import example_complete_workflow
await example_complete_workflow()
```

## Architecture Benefits

1. **Testability**: Easy to mock repositories for unit testing
2. **Flexibility**: Easy to swap HTTP client for different implementations
3. **Maintainability**: Clear separation of concerns
4. **Extensibility**: Easy to add new operations or modify existing ones
5. **Type Safety**: Full type checking with Pydantic models
6. **Error Handling**: Comprehensive error handling at all layers

## Dependencies

- `httpx`: Modern async HTTP client
- `pydantic`: Data validation and serialization
- `src.external.idaas.client`: IDaaS token management
- `src.config.settings`: Application configuration
