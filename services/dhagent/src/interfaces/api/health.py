"""Health check and basic application endpoints."""

from fastapi import APIRouter

from src.config.settings import settings
from src.interfaces.api.spec import Response, SuccessResponse

router = APIRouter()


@router.get("/")
async def root() -> Response:
    """Root endpoint that returns a welcome message."""

    return SuccessResponse(
        data={"message": f"Welcome to {settings.app_name}"},
    )


@router.get("/health")
async def health_check() -> Response:
    """Health check endpoint."""
    return SuccessResponse(data={"status": "healthy", "service": "dhagent"})


@router.get("/info")
async def get_info() -> Response:
    """Get application information."""
    return SuccessResponse(
        data={
            "name": settings.app_name,
            "version": settings.app_version,
            "description": settings.app_description,
        }
    )
