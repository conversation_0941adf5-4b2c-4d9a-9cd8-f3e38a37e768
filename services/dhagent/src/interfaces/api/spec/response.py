from typing import Any

from pydantic import BaseModel, Field


class Pagination(BaseModel):
    page: int | None = Field(default=None, description="页码")
    size: int | None = Field(default=None, description="单页大小")
    total: int | None = Field(default=None, description="数据总条数")


class SuccessResponse(BaseModel):
    """
    标准成功响应体结构
    """

    code: str | None = Field(
        default=None,
        description="自定义成功名称，必须是 screaming snake case 的字符串常量",
        examples=["TICKET_CREATED", "TICKET_UPDATED"],
    )

    message: str | None = Field(default=None, description="自定义成功消息，任意可读的自然语言字符串")

    data: dict[str, Any] | list[Any] | BaseModel = Field(..., description="请求成功时，可以是单个对象，也可以是数组")


class SuccessPaginationResponse(SuccessResponse):
    """
    成功响应体结构，包含分页信息
    """

    pagination: Pagination = Field(..., description="分页信息，包含页码、单页大小和总条数")


class Detail(BaseModel):
    """
    任意对象，包含类型信息
    """

    field_type: str = Field(..., alias="@type", description="对象的类型标识")


class ErrorResponse(BaseModel):
    """
    标准错误响应体结构
    """

    code: str = Field(
        ...,
        description="自定义错误名称，必须是 screaming snake case 的字符串常量",
        examples=["INVALID_PARAM_TICKET_NAME"],
    )
    message: str | None = Field(default=None, description="自定义错误消息，任意可读的自然语言字符串")
    details: list[Detail] | None = Field(default=None, description="细节信息列表")


Response = SuccessResponse | ErrorResponse
