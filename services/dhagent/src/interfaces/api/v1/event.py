"""Handle agent events"""

import asyncio
import logging
from typing import Any

from cloudevents.pydantic import CloudEvent
from fastapi import APIRouter, Request, status
from pydantic import BaseModel, Field

from src.interfaces.api.spec import SuccessResponse
from src.usecases.event_distribution import default_cloud_event_distributor


class SimpleCloudEvent(BaseModel):
    """A simplified CloudEvent representation that FastAPI can properly document."""

    id: str = Field(..., description="Unique identifier of the event")
    source: str = Field(..., description="Source of the event")
    type: str = Field(..., description="Event type")
    specversion: str = Field(..., description="CloudEvent specification version")
    subject: str | None = Field(None, description="Subject of the event")
    time: str | None = Field(None, description="Event timestamp in ISO format")
    datacontenttype: str | None = Field(None, description="Content type of the data")
    dataschema: str | None = Field(None, description="Schema of the data")
    data: Any | None = Field(None, description="Event data payload")

    @classmethod
    def from_cloud_event(cls, event: CloudEvent) -> "SimpleCloudEvent":
        """Transform a CloudEvent into a SimpleCloudEvent."""
        return cls(
            id=event.id,
            source=event.source,
            type=event.type,
            specversion=str(event.specversion),
            subject=getattr(event, "subject", None),
            time=event.time.isoformat() if event.time else None,
            datacontenttype=getattr(event, "datacontenttype", None),
            dataschema=getattr(event, "dataschema", None),
            data=event.data,
        )


class EventAcceptedResponse(SuccessResponse):
    data: SimpleCloudEvent


logger = logging.getLogger(__name__)
router = APIRouter(prefix="/events")


@router.post("/", status_code=status.HTTP_202_ACCEPTED)
async def handle_event(request: Request) -> EventAcceptedResponse:
    """Handle an incoming CloudEvent."""
    # Parse the CloudEvent from the HTTP request
    event: CloudEvent = CloudEvent.model_validate_json(await request.body())

    # Placeholder for event handling logic
    # This could involve processing the event, updating state, etc.

    # Schedule the distributor to run in the background with error handling
    asyncio.create_task(_distribute_event_with_error_handling(event))

    # Transform CloudEvent to SimpleCloudEvent for proper API documentation
    simple_event = SimpleCloudEvent.from_cloud_event(event)

    return EventAcceptedResponse(data=simple_event)


async def _distribute_event_with_error_handling(event: CloudEvent) -> None:
    """Distribute event in background with proper error handling."""
    try:
        await default_cloud_event_distributor.distribute(event)
        logger.info(f"Successfully distributed event {event.id}")
    except Exception as e:
        logger.error(f"Failed to distribute event {event.id}: {e}", exc_info=True)
        # You could also:
        # - Send to a dead letter queue
        # - Retry with exponential backoff
        # - Send alerts/notifications
