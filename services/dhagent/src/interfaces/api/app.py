"""FastAPI application factory and configuration."""

import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.config.settings import settings
from src.interfaces.api.health import router as health_router
from src.interfaces.api.v1 import v1_router

# Configure application logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="[v1] [%(asctime)s.%(msecs)04d] [%(levelname)s] [%(thread)d] %(filename)s "
    + "[TID: N/A] [%(module)s] [%(funcName)s] %(lineno)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler()],
)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""

    app = FastAPI(
        title=settings.app_name,
        description=settings.app_description,
        version=settings.app_version,
        debug=settings.debug,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.backend_cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(health_router, tags=["health"])
    app.include_router(v1_router, prefix="/api")

    return app


# Create the app instance
app = create_app()
