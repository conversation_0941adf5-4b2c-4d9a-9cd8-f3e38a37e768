from abc import ABC, abstractmethod
from logging import getLogger

from cloudevents.pydantic import CloudEvent

from src.usecases.change_management import ChangeEventHandler

from .base import CloudEventHandler

logger = getLogger(__name__)


class CloudEventDistributor(ABC):
    """Abstract base class for event distributors."""

    @abstractmethod
    async def distribute(self, event: CloudEvent) -> None:
        """Distribute an event to the appropriate handlers."""
        pass

    @abstractmethod
    def register_handler(self, handler: CloudEventHandler) -> None:
        """Register a handler for events."""
        pass


class SimpleCloudEventDistributor(CloudEventDistributor):
    """A simple event distributor."""

    handlers: list[CloudEventHandler] = []

    async def distribute(self, event: CloudEvent) -> None:
        """No operation for distributing events."""
        logger.info(f"Received event: {event.model_dump()}")

        for handler in self.handlers:
            if await handler.should_handle(event):
                logger.info(f"Handling event with {handler.__class__.__name__}")
                await handler.handle_event(event)

    def register_handler(self, handler: CloudEventHandler) -> None:
        if handler not in self.handlers:
            self.handlers.append(handler)
            logger.info(f"Registered handler: {handler.__class__.__name__}")
        else:
            logger.info(f"Handler {handler.__class__.__name__} is already registered.")


default_cloud_event_distributor = SimpleCloudEventDistributor()
default_cloud_event_distributor.register_handler(ChangeEventHandler())
