from abc import ABC, abstractmethod

from cloudevents.pydantic import CloudEvent


class CloudEventHandler(ABC):
    """Abstract base class for handling CloudEvents."""

    @abstractmethod
    async def should_handle(self, event: CloudEvent) -> bool:
        """
        Determine if the handler should process the given event.

        Args:
            event CloudEvent: The event to check.

        Returns:
            bool: True if the handler should process the event, False otherwise.
        """
        pass

    @abstractmethod
    async def handle_event(self, event: CloudEvent) -> None:
        """
        Handle an incoming event.

        Args:
            event CloudEvent: The event to handle.
        """
        pass
