from langgraph.graph import MessagesState
from langgraph.graph.state import CompiledStateGraph
from pydantic import BaseModel, Field

from src.usecases.shared import ComposableAgent


class Prediction(BaseModel):
    """Represents a single incident prediction."""

    description: str = Field(..., description="Description of the predicted incident")
    confidence: float = Field(..., description="Confidence level of the prediction")


class IncidentPredictionState(MessagesState):
    # TODO: input definition @chenyuan1

    # ouput
    prediction: Prediction | None


class IncidentPredictor(ComposableAgent):
    """
    Abstract base class for incident prediction use cases.
    """

    @property
    def graph(self) -> CompiledStateGraph:
        """
        Returns the compiled state graph for the incident prediction use case.
        """
        raise NotImplementedError("Subclasses must implement this method.")
