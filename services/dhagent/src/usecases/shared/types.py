"""
Base interfaces and protocols for usecases.

This module contains shared abstractions that can be used across multiple usecases
while maintaining clean architecture principles.
"""

from abc import ABC, abstractmethod

from langgraph.graph.state import CompiledStateGraph


class ComposableAgent(ABC):
    """
    Abstract base class for composable agents.
    """

    @property
    @abstractmethod
    def graph(self) -> CompiledStateGraph:
        pass
