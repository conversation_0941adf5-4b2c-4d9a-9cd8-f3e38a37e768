"""
Logs Health Checker

This module provides high-level log analysis tools for AI agents to assess component health
based on log patterns, error rates, and anomalies. It abstracts away low-level log queries
and provides intelligent insights about log-based health indicators.

## Design Philosophy

### Focused Anomaly Detection
Instead of complex categorization, this module focuses on the most critical log anomalies:
- **Error Spikes**: Sudden increases in error-level logs that indicate system issues
- **Unusual Patterns**: Abnormal log volume or message patterns that deviate from baseline

### Intelligent Pattern Analysis
The module analyzes log patterns by:
1. **Error Rate Analysis**: Tracks error logs per minute and compares to baseline
2. **Volume Pattern Detection**: Identifies unusual increases/decreases in log volume
3. **Message Pattern Recognition**: Detects recurring error patterns and anomalies
4. **Baseline Comparison**: Uses historical data to identify deviations

### AI-Friendly Interface
Provides a single function interface that gives AI agents:
- Clear health status assessment
- Actionable insights about log anomalies
- Confidence scoring for assessment reliability
- Trend analysis for proactive monitoring

### Example Usage

```python
# Standard health check
request = CheckLogsRequest(
    component="user-service",
    env="prod",
    biz_cluster="main-cluster",
    duration_minutes=30,       # Analyze last 30 minutes
    baseline_offset_minutes=1440   # Compare against 24h ago
)

# Post-deployment analysis
request = CheckLogsRequest(
    component="user-service",
    env="prod",
    biz_cluster="main-cluster",
    duration_minutes=10,       # Analyze 10 minutes after deployment
    baseline_offset_minutes=20     # Compare against 10min before deployment
)

health = check_logs(request)

# High-level assessment
if health.overall_status == LogHealthStatus.CRITICAL:
    print(f"🚨 Log health: {health.overall_score:.1f}/100")
    for finding in health.key_findings:
        print(f"   Issue: {finding}")
    for rec in health.recommendations:
        print(f"   Action: {rec}")

# Detailed analysis
analysis = health.log_analysis
print(f"Error rate: {analysis.error_rate:.1f} errors/min")
print(f"Total logs: {analysis.total_count}")
print(f"Anomalies: {analysis.anomalies}")
```

### Intelligence Features

The logs checker provides sophisticated analysis:

```python
# Error spike detection
if LogAnomalyType.ERROR_SPIKE in analysis.anomalies:
    print(f"Error spike detected: {analysis.error_rate:.1f} errors/min")
    print(f"Baseline comparison: {analysis.baseline_comparison:+.1f}% change")

# Pattern analysis
for pattern, count in analysis.pattern_analysis.items():
    print(f"Pattern '{pattern}': {count} occurrences")

# Trend insights
print(f"Log volume trend: {analysis.trend_direction}")
```

This enables AI agents to quickly identify log-based issues without needing to understand
complex log query syntax or pattern matching logic.
"""

from datetime import datetime
from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class LogAnomalyType(str, Enum):
    """Types of log anomalies that can be detected"""

    ERROR_SPIKE = "error_spike"
    UNUSUAL_PATTERN = "unusual_pattern"


class LogHealthStatus(str, Enum):
    """Health status levels for log analysis"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class LogAnalysisSummary(BaseModel):
    """Summary of log analysis for a component"""

    total_count: int = Field(..., description="Total number of logs analyzed")
    error_count: int = Field(..., description="Number of error-level logs")
    error_rate: float = Field(..., description="Error logs per minute")
    total_rate: float = Field(..., description="Total logs per minute")
    anomalies: list[LogAnomalyType] = Field(default_factory=list, description="Detected log anomalies")
    pattern_analysis: dict[str, int] = Field(default_factory=dict, description="Key patterns and their frequencies")
    trend_direction: Literal["increasing", "stable", "decreasing", "unknown"] = Field(
        "unknown", description="Overall log volume trend"
    )
    baseline_comparison: float | None = Field(None, description="Percentage change from baseline period")


class ComponentLogsReport(BaseModel):
    """Comprehensive log health assessment for a component"""

    component: str = Field(..., description="Component identifier")
    env: LiCloudEnv = Field(..., description="Environment")
    timestamp: datetime = Field(..., description="When the assessment was performed")
    overall_status: LogHealthStatus = Field(..., description="Overall log health status")
    overall_score: float = Field(..., description="Overall health score (0-100, higher is better)")

    log_analysis: LogAnalysisSummary = Field(..., description="Detailed log analysis")

    key_findings: list[str] = Field(default_factory=list, description="Critical insights from log analysis")
    recommendations: list[str] = Field(default_factory=list, description="Recommended actions")
    confidence: float = Field(..., description="Confidence in assessment (0-100)")


class CheckLogsRequest(BaseModel):
    """Request to check log health for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    biz_cluster: str = Field(..., description="Business cluster identifier")
    duration_minutes: int = Field(default=30, description="Duration of each comparison period (minutes)", ge=1, le=1440)
    baseline_offset_minutes: int = Field(
        default=1440, description="How far back to look for baseline comparison (minutes)", ge=1, le=10080
    )
    include_insights: bool = Field(default=True, description="Include analysis insights and recommendations")
    reference_time: datetime = Field(
        default_factory=datetime.now,
        description="Reference time for calculations. Defaults to current time if not specified.",
    )


def check_logs(request: CheckLogsRequest) -> ComponentLogsReport:
    """
    Perform comprehensive log health check for a component.

    This is the main interface for AI agents to assess component health based on log patterns,
    error rates, and anomalies without needing to understand low-level log queries.

    Timeline Analysis:
        The function compares two equal-duration periods for fair statistical comparison:

        ```
        # Example: Deployment analysis (10min check, 20min lookback):
        # |-- 10 min --|    [gap: 20 min]    |-- 10 min --|
        # baseline_start baseline_end         start_time   end_time
        #     ↑               ↑                 ↑           ↑
        # 30min ago       20min ago       10min ago      now

        # Example: Daily comparison (30min check, 24h lookback):
        # |-- 30 min --|    [gap: 24 hours]    |-- 30 min --|
        # baseline_start baseline_end           start_time   end_time
        #     ↑               ↑                     ↑           ↑
        # 25h ago         24h ago            30min ago      now
        ```

        Common scenarios:
        - Deployment analysis: duration_minutes=10, baseline_offset_minutes=20
          (Compare 10min after deployment vs 10min before deployment)
        - Hourly monitoring: duration_minutes=30, baseline_offset_minutes=60
          (Compare last 30min vs 30min from 1 hour ago)
        - Daily comparison: duration_minutes=30, baseline_offset_minutes=1440
          (Compare last 30min vs same time yesterday)

    Anomaly Detection:
        - **Error Spike**: Detects sudden increases in error logs compared to baseline
        - **Unusual Pattern**: Identifies abnormal log volume or message patterns

    Args:
        request: Log health check parameters

    Returns:
        Comprehensive log health assessment report
    """
    # Implementation will be added later
    raise NotImplementedError("Implementation pending")
