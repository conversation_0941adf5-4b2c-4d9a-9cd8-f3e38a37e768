"""
Traces Health Checker

This module provides high-level distributed tracing analysis tools for AI agents to assess
component health based on trace patterns, error rates, and service interactions. It focuses
on trace-specific concerns that complement the golden signals checker.

## Design Philosophy

### Trace-Specific Focus
This module focuses on distributed tracing concerns that aren't covered by golden signals:
- **Error Traces**: Which traces are failing and error patterns
- **Timeout Detection**: Traces that don't complete successfully
- **Trace Volume**: Changes in trace generation patterns
- **Service Complexity**: Analysis of trace structure and span relationships

### Complementary to Golden Signals
While golden signals checker handles latency metrics (P95, average response times),
traces checker provides deeper insights into:
- Distributed system interactions
- Service dependency failures
- Trace-level error patterns
- System complexity trends

### Intelligent Trace Analysis
The module analyzes traces by:
1. **Error Pattern Detection**: Identifies failing trace patterns and root causes
2. **Timeout Analysis**: Detects traces that fail to complete
3. **Volume Pattern Recognition**: Unusual increases/decreases in trace generation
4. **Complexity Assessment**: Changes in service interactions and trace structure

### AI-Friendly Interface
Provides focused insights for AI agents:
- Clear error rate and timeout assessments
- Service interaction health analysis
- Complexity trend detection
- Actionable recommendations for distributed system issues

### Example Usage

```python
# Standard trace health check
request = CheckTracesRequest(
    component="user-service",
    env="prod",
    biz_cluster="main-cluster",
    duration_minutes=30,       # Analyze last 30 minutes
    baseline_offset_minutes=1440   # Compare against 24h ago
)

# Post-deployment trace analysis
request = CheckTracesRequest(
    component="payment-service",
    env="prod",
    biz_cluster="main-cluster",
    duration_minutes=10,       # Analyze 10 minutes after deployment
    baseline_offset_minutes=20     # Compare against 10min before deployment
)

report = check_traces(request)

# High-level assessment
if report.overall_status == TraceHealthStatus.CRITICAL:
    print(f"🚨 Trace health: {report.overall_score:.1f}/100")
    for finding in report.key_findings:
        print(f"   Issue: {finding}")
    for rec in report.recommendations:
        print(f"   Action: {rec}")

# Detailed trace analysis
analysis = report.trace_analysis
print(f"Error rate: {analysis.error_rate_pct:.1f}% of traces")
print(f"Timeout rate: {analysis.timeout_rate_pct:.1f}% of traces")
print(f"Trace complexity: {analysis.avg_span_count:.1f} spans per trace")
print(f"Anomalies: {analysis.anomalies}")
```

### Intelligence Features

The traces checker provides sophisticated analysis:

```python
# Error pattern detection
if TraceAnomalyType.ERROR_TRACES in analysis.anomalies:
    print(f"Error traces detected: {analysis.error_rate_pct:.1f}%")
    if analysis.baseline_comparison:
        print(f"Error rate change: {analysis.baseline_comparison.error_rate_change_pct:+.1f}%")

# Timeout analysis
if TraceAnomalyType.TIMEOUT_TRACES in analysis.anomalies:
    print(f"Timeout rate: {analysis.timeout_rate_pct:.1f}%")

# Complexity insights
if analysis.baseline_comparison and analysis.baseline_comparison.avg_span_count_change_pct:
    change = analysis.baseline_comparison.avg_span_count_change_pct
    print(f"Trace complexity changed by {change:+.1f}%")

# Volume analysis
print(f"Total traces analyzed: {analysis.total_traces}")
print(f"Trace trend: {analysis.trend_direction}")
```

This enables AI agents to understand distributed system health from a service interaction
perspective, complementing the performance metrics from golden signals checker.
"""

from datetime import datetime
from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class TraceAnomalyType(str, Enum):
    """Types of trace anomalies that can be detected"""

    ERROR_TRACES = "error_traces"
    TIMEOUT_TRACES = "timeout_traces"
    UNUSUAL_VOLUME = "unusual_volume"
    UNUSUAL_COMPLEXITY = "unusual_complexity"


class TraceHealthStatus(str, Enum):
    """Health status levels for trace analysis"""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class TraceBaselineComparison(BaseModel):
    """Baseline comparison metrics for traces"""

    error_rate_change_pct: float | None = Field(None, description="Percentage change in trace error rate")
    trace_volume_change_pct: float | None = Field(None, description="Percentage change in number of traces")
    timeout_rate_change_pct: float | None = Field(None, description="Percentage change in timeout rate")
    avg_span_count_change_pct: float | None = Field(None, description="Percentage change in trace complexity")


class TraceAnalysisSummary(BaseModel):
    """Summary of trace analysis for a component"""

    total_traces: int = Field(..., description="Total number of traces analyzed")
    error_trace_count: int = Field(..., description="Number of traces with errors")
    timeout_trace_count: int = Field(..., description="Number of traces that timed out")
    error_rate_pct: float = Field(..., description="Percentage of traces with errors")
    timeout_rate_pct: float = Field(..., description="Percentage of traces that timed out")
    avg_span_count: float = Field(..., description="Average spans per trace (complexity indicator)")
    anomalies: list[TraceAnomalyType] = Field(default_factory=list, description="Detected trace anomalies")
    trend_direction: Literal["improving", "stable", "degrading", "unknown"] = Field(
        "unknown", description="Overall trace health trend"
    )
    baseline_comparison: TraceBaselineComparison | None = Field(None, description="Baseline comparison metrics")


class ComponentTracesReport(BaseModel):
    """Comprehensive trace health assessment for a component"""

    component: str = Field(..., description="Component identifier")
    env: LiCloudEnv = Field(..., description="Environment")
    timestamp: datetime = Field(..., description="When the assessment was performed")
    overall_status: TraceHealthStatus = Field(..., description="Overall trace health status")
    overall_score: float = Field(..., description="Overall health score (0-100, higher is better)")

    trace_analysis: TraceAnalysisSummary = Field(..., description="Detailed trace analysis")

    key_findings: list[str] = Field(default_factory=list, description="Critical insights from trace analysis")
    recommendations: list[str] = Field(default_factory=list, description="Recommended actions")
    confidence: float = Field(..., description="Confidence in assessment (0-100)")


class CheckTracesRequest(BaseModel):
    """Request to check trace health for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    biz_cluster: str = Field(..., description="Business cluster identifier")
    duration_minutes: int = Field(default=30, description="Duration of each comparison period (minutes)", ge=1, le=1440)
    baseline_offset_minutes: int = Field(
        default=1440, description="How far back to look for baseline comparison (minutes)", ge=1, le=10080
    )
    include_insights: bool = Field(default=True, description="Include analysis insights and recommendations")
    reference_time: datetime = Field(
        default_factory=datetime.now,
        description="Reference time for calculations. Defaults to current time if not specified.",
    )


def check_traces(request: CheckTracesRequest) -> ComponentTracesReport:
    """
    Perform comprehensive trace health check for a component.

    This is the main interface for AI agents to assess component health based on distributed
    tracing patterns, focusing on trace-specific concerns that complement golden signals metrics.

    Timeline Analysis:
        The function compares two equal-duration periods for fair statistical comparison:

        ```
        # Example: Deployment analysis (10min check, 20min lookback):
        # |-- 10 min --|    [gap: 20 min]    |-- 10 min --|
        # baseline_start baseline_end         start_time   end_time
        #     ↑               ↑                 ↑           ↑
        # 30min ago       20min ago       10min ago      now

        # Example: Daily comparison (30min check, 24h lookback):
        # |-- 30 min --|    [gap: 24 hours]    |-- 30 min --|
        # baseline_start baseline_end           start_time   end_time
        #     ↑               ↑                     ↑           ↑
        # 25h ago         24h ago            30min ago      now
        ```

        Common scenarios:
        - Deployment analysis: duration_minutes=10, baseline_offset_minutes=20
          (Compare 10min after deployment vs 10min before deployment)
        - Hourly monitoring: duration_minutes=30, baseline_offset_minutes=60
          (Compare last 30min vs 30min from 1 hour ago)
        - Daily comparison: duration_minutes=30, baseline_offset_minutes=1440
          (Compare last 30min vs same time yesterday)

    Trace-Specific Analysis:
        - **Error Traces**: Detects increases in failing traces and error patterns
        - **Timeout Detection**: Identifies traces that fail to complete
        - **Volume Analysis**: Unusual increases/decreases in trace generation
        - **Complexity Assessment**: Changes in service interactions (span count trends)

    Complementary Focus:
        This checker focuses on distributed tracing concerns while golden_signals_checker
        handles latency metrics (P95, average response times). Together they provide
        comprehensive observability coverage.

    Args:
        request: Trace health check parameters

    Returns:
        Comprehensive trace health assessment report
    """
    # Implementation will be added later
    raise NotImplementedError("Implementation pending")
