"""
Topology Health Checker

This module provides service topology analysis tools for AI agents to identify which services
are experiencing errors and understand error patterns across the distributed system topology.
It focuses on error spotting and propagation analysis rather than complex health scoring.

## Design Philosophy

### Error Spotting Focus
The primary purpose is to identify:
- **Services with Errors**: Which services currently have alerts, error logs, or error traces
- **Error Patterns**: How errors spread through service dependencies
- **Error Hotspots**: Services with concentrated error activity
- **Cascading Failures**: Error chains through service topology

### Simple and Direct
Instead of complex health scoring, this module provides:
- Clear identification of problematic services
- Error counts and types per service
- Dependency chain analysis for error propagation
- Straightforward error pattern descriptions

### Topology-Aware Error Analysis
Leverages service topology to:
1. **Map Error Distribution**: Show which services in a topology have errors
2. **Trace Error Propagation**: Identify how errors spread through dependencies
3. **Find Error Clusters**: Detect groups of interconnected services with issues
4. **Assess Impact Scope**: Understand how many services are affected

### AI-Friendly Interface
Provides clear, actionable information:
- List of services with error details
- Error propagation paths through topology
- Simple anomaly detection (hotspots, cascading failures)
- Focused insights without complex scoring

### Example Usage

```python
# Standard topology error check
request = CheckTopologyRequest(
    component="order-service",
    env="prod",
    topology_depth=3,          # Analyze 3 hops around order-service
    check_duration_minutes=30, # Analyze last 30 minutes
    baseline_duration_minutes=1440  # Compare against 24h ago
)

# Post-deployment topology analysis
request = CheckTopologyRequest(
    component="payment-service",
    env="prod",
    topology_depth=2,          # Analyze 2 hops around payment-service
    check_duration_minutes=10, # Analyze 10 minutes after deployment
    baseline_duration_minutes=20  # Compare against 10min before deployment
)

report = check_topology(request)

# Identify services with errors
analysis = report.topology_analysis
print(f"Services analyzed: {analysis.total_services}")
print(f"Services with errors: {len(analysis.services_with_errors)}")

for service_error in analysis.services_with_errors:
    print(f"  {service_error.service_name}: {service_error.alert_count} alerts, "
          f"{service_error.error_log_count} log errors, {service_error.error_trace_count} trace errors")

    # Show baseline comparison if available
    if service_error.baseline_alert_count is not None:
        print(f"    (baseline: {service_error.baseline_alert_count} alerts, "
              f"{service_error.baseline_error_log_count} logs, {service_error.baseline_error_trace_count} traces)")

# Check for new/resolved error services
if analysis.new_error_services:
    print(f"New error services since baseline: {analysis.new_error_services}")
if analysis.resolved_error_services:
    print(f"Resolved error services since baseline: {analysis.resolved_error_services}")

# Check for error patterns
for pattern in analysis.error_patterns:
    print(f"Error pattern: {pattern}")

# Review findings
for finding in report.key_findings:
    print(f"Finding: {finding}")
```

### Intelligence Features

The topology checker provides focused error analysis:

```python
# Error hotspot detection
if TopologyAnomalyType.ERROR_HOTSPOTS in analysis.anomalies:
    hotspots = [s for s in analysis.services_with_errors
                if s.alert_count + s.error_log_count + s.error_trace_count > 10]
    print(f"Error hotspots detected: {[h.service_name for h in hotspots]}")

# Cascading failure analysis
if TopologyAnomalyType.CASCADING_FAILURES in analysis.anomalies:
    print("Cascading failure patterns found in error_patterns")

# Dependency error impact
for service_error in analysis.services_with_errors:
    if service_error.dependencies_with_errors:
        print(f"{service_error.service_name} has errors and depends on: "
              f"{service_error.dependencies_with_errors}")
```

This enables AI agents to quickly identify problematic services and understand how
errors are distributed across the service topology without complex health calculations.
"""

from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class TopologyAnomalyType(str, Enum):
    """Types of topology anomalies focused on error patterns"""

    ERROR_HOTSPOTS = "error_hotspots"
    CASCADING_FAILURES = "cascading_failures"


class TopologyHealthStatus(str, Enum):
    """Simple health status levels for topology analysis"""

    HEALTHY = "healthy"
    ISSUES_DETECTED = "issues_detected"
    UNKNOWN = "unknown"


class ServiceErrorInfo(BaseModel):
    """Error information for a service in the topology"""

    service_name: str = Field(..., description="Service identifier")
    alert_count: int = Field(..., description="Number of active alerts")
    error_log_count: int = Field(..., description="Number of error logs")
    error_trace_count: int = Field(..., description="Number of error traces")
    has_errors: bool = Field(..., description="Whether this service has any errors")
    dependencies_with_errors: list[str] = Field(
        default_factory=list, description="Names of dependencies that also have errors"
    )
    # Baseline comparison
    baseline_alert_count: int | None = Field(None, description="Alert count in baseline period")
    baseline_error_log_count: int | None = Field(None, description="Error log count in baseline period")
    baseline_error_trace_count: int | None = Field(None, description="Error trace count in baseline period")


class TopologyAnalysisSummary(BaseModel):
    """Summary of topology error analysis"""

    total_services: int = Field(..., description="Total number of services analyzed in topology")
    services_with_errors: list[ServiceErrorInfo] = Field(
        default_factory=list, description="Services that have errors (main focus)"
    )
    error_patterns: list[str] = Field(default_factory=list, description="Human-readable descriptions of error patterns")
    anomalies: list[TopologyAnomalyType] = Field(default_factory=list, description="Detected topology error anomalies")
    # Baseline comparison
    baseline_services_with_errors_count: int | None = Field(
        None, description="Number of services with errors in baseline period"
    )
    new_error_services: list[str] = Field(
        default_factory=list, description="Services that have new errors compared to baseline"
    )
    resolved_error_services: list[str] = Field(
        default_factory=list, description="Services that had errors in baseline but not now"
    )


class ComponentTopologyReport(BaseModel):
    """Topology error analysis report for a component"""

    component: str = Field(..., description="Focus component for topology analysis")
    env: LiCloudEnv = Field(..., description="Environment")
    timestamp: datetime = Field(..., description="When the analysis was performed")
    overall_status: TopologyHealthStatus = Field(..., description="Simple overall status")

    topology_analysis: TopologyAnalysisSummary = Field(..., description="Topology error analysis results")

    key_findings: list[str] = Field(default_factory=list, description="Key insights about service errors and patterns")


class CheckTopologyRequest(BaseModel):
    """Request to check topology for error patterns"""

    component: str = Field(..., description="Focus component for topology analysis")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    topology_depth: int = Field(
        default=3, description="Number of hops to analyze around the focus component", ge=1, le=5
    )
    check_duration_minutes: int = Field(default=30, description="Duration to analyze (minutes)", ge=1, le=1440)
    baseline_duration_minutes: int = Field(
        default=1440, description="Historical baseline lookback period (minutes)", ge=1, le=10080
    )
    include_insights: bool = Field(default=True, description="Include error pattern insights")


def check_topology(request: CheckTopologyRequest) -> ComponentTopologyReport:
    """
    Perform topology error analysis to identify services with errors and error patterns.

    This is the main interface for AI agents to understand which services in a topology
    are experiencing errors and how those errors are distributed or propagating through
    the service dependency graph.

    Analysis Focus:
        - **Service Error Identification**: Find all services with alerts, error logs, or error traces
        - **Error Distribution Mapping**: Show how errors are spread across the topology
        - **Dependency Error Analysis**: Identify services whose dependencies also have errors
        - **Error Pattern Detection**: Recognize hotspots and cascading failure patterns
        - **Baseline Comparison**: Compare current error patterns with historical baseline

    Timeline Analysis:
        The function compares two equal-duration periods for fair statistical comparison:

        ```
        # Example: Deployment analysis (10min check, 20min lookback):
        # |-- 10 min --|    [gap: 20 min]    |-- 10 min --|
        # baseline_start baseline_end         start_time   end_time
        #     ↑               ↑                 ↑           ↑
        # 30min ago       20min ago       10min ago      now

        # Example: Daily comparison (30min check, 24h lookback):
        # |-- 30 min --|    [gap: 24 hours]    |-- 30 min --|
        # baseline_start baseline_end           start_time   end_time
        #     ↑               ↑                     ↑           ↑
        # 25h ago         24h ago            30min ago      now
        ```

        Common scenarios:
        - Deployment analysis: check_duration_minutes=10, baseline_duration_minutes=20
          (Compare 10min after deployment vs 10min before deployment)
        - Hourly monitoring: check_duration_minutes=30, baseline_duration_minutes=60
          (Compare last 30min vs 30min from 1 hour ago)
        - Daily comparison: check_duration_minutes=30, baseline_duration_minutes=1440
          (Compare last 30min vs same time yesterday)

    Topology Scope:
        The analysis centers around the specified component and expands outward based on
        topology_depth to include connected services:

        ```
        depth=1: component + direct dependencies/dependents
        depth=2: component + 1-hop + 2-hop connections
        depth=3: component + 1-hop + 2-hop + 3-hop connections
        ```

    Error Analysis:
        For each service in the topology scope:
        1. Retrieve current error statistics (alerts, logs, traces) for check period
        2. Retrieve baseline error statistics for comparison period
        3. Determine if service has errors (any count > 0)
        4. Check which of its dependencies also have errors
        5. Identify error propagation patterns and hotspots
        6. Compare current vs baseline to find new/resolved error services

    Output Focus:
        - Clear list of services with errors and error counts (current vs baseline)
        - New error services (services that developed errors since baseline)
        - Resolved error services (services that had errors in baseline but not now)
        - Error propagation chains through dependencies
        - Simple anomaly detection (hotspots, cascading failures)
        - Factual insights about error pattern changes

    Args:
        request: Topology error analysis parameters

    Returns:
        Topology error analysis report focused on service error identification
    """
    # Implementation will be added later
    raise NotImplementedError("Implementation pending")
