from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv

SeverityLevel = Literal["critical", "high", "medium", "low"]
AlertStatus = Literal["active", "resolved", "suppressed", "acknowledged"]


class Alert(BaseModel):
    id: str = Field(..., description="Unique alert identifier")
    title: str = Field(..., description="Alert title or summary")
    description: str = Field(..., description="Detailed alert description")
    severity: SeverityLevel = Field(..., description="Alert severity level")
    status: AlertStatus = Field(..., description="Current alert status")
    component: str = Field(..., description="OAM component that triggered the alert")
    env: LiCloudEnv = Field(..., description="Environment where alert occurred")
    created_at: datetime = Field(..., description="When the alert was created")
    updated_at: datetime = Field(..., description="When the alert was last updated")
    tags: list[str] = Field(default_factory=list, description="Alert tags for categorization")
    metrics: dict[str, float] = Field(default_factory=dict, description="Related metric values")


class ListAlertsRequest(BaseModel):
    component: str | None = Field(None, description="Filter by OAM component")
    env: LiCloudEnv = Field(default="dev", description="Environment to query")
    severity: SeverityLevel | None = Field(None, description="Filter by severity level")
    status: AlertStatus | None = Field(None, description="Filter by alert status")
    limit: int = Field(default=50, description="Maximum number of alerts to return")
    start_time: datetime | None = Field(None, description="Start time for filtering alerts")
    end_time: datetime | None = Field(None, description="End time for filtering alerts")


def list_alerts(request: ListAlertsRequest) -> list[Alert]:
    """
    List alerts based on the provided filtering criteria.

    Args:
        request (ListAlertsRequest): Request parameters for listing alerts

    Returns:
        list[Alert]: List of alerts matching the criteria
    """
    # Placeholder for actual alert listing logic
    return []


class SearchAlertsRequest(BaseModel):
    query: str = Field(
        ...,
        description="Search query string to filter alerts, allows for semantic search",
    )
    component: str | None = Field(None, description="Filter by OAM component")
    env: LiCloudEnv = Field(default="dev", description="Environment to search in")
    severity: SeverityLevel | None = Field(None, description="Filter by severity")
    status: AlertStatus | None = Field(None, description="Filter by status")
    limit: int = Field(default=50, description="Maximum number of alerts to return")


def search_alerts(request: SearchAlertsRequest) -> list[Alert]:
    """
    Search for alerts based on a query string.

    Args:
        request (SearchAlertRequest): Search query parameters

    Returns:
        list[Alert]: List of alerts matching the search criteria
    """
    # Placeholder for actual alert search logic
    return []


class AcknowledgeAlertRequest(BaseModel):
    alert_id: str = Field(..., description="ID of the alert to acknowledge")
    comment: str | None = Field(None, description="Optional comment for acknowledgment")


def acknowledge_alert(request: AcknowledgeAlertRequest) -> bool:
    """
    Acknowledge an alert to indicate it's being handled.

    Args:
        request (AcknowledgeAlertRequest): Alert acknowledgment details

    Returns:
        bool: True if acknowledgment was successful
    """
    # Placeholder for actual alert acknowledgment logic
    return False


class SuppressAlertRequest(BaseModel):
    alert_id: str = Field(..., description="ID of the alert to suppress")
    duration_hours: int = Field(default=24, description="How long to suppress the alert")
    reason: str = Field(..., description="Reason for suppressing the alert")


def suppress_alert(request: SuppressAlertRequest) -> bool:
    """
    Suppress an alert for a specified duration.

    Args:
        request (SuppressAlertRequest): Alert suppression details

    Returns:
        bool: True if suppression was successful
    """
    # Placeholder for actual alert suppression logic
    return False


class SummarizeAlertPatternsRequest(BaseModel):
    env: LiCloudEnv = Field(default="dev", description="Environment to summarize alerts for")
    component: str | None = Field(None, description="OAM component to filter alerts")
    hours: int = Field(default=24, description="Number of hours to look back for alert patterns")
    limit: int = Field(default=10, description="Maximum number of patterns to return")


class AlertPattern(BaseModel):
    pattern: str = Field(..., description="Description of the alert pattern")
    count: int = Field(..., description="Number of occurrences of this pattern")
    severity: SeverityLevel = Field(..., description="Most common severity level in this pattern")


class SummarizeAlertPatternsResponse(BaseModel):
    patterns: list[AlertPattern] = Field(..., description="List of identified alert patterns")


def summarize_alert_patterns(
    request: SummarizeAlertPatternsRequest,
) -> SummarizeAlertPatternsResponse:
    """
    Summarize alert patterns for a component.

    Args:
        request (SummarizeAlertPatternsRequest): Request parameters for summarizing alert patterns

    Returns:
        SummarizeAlertPatternsResponse: Summary of alert patterns
    """
    # Placeholder for actual alert pattern summarization logic
    return SummarizeAlertPatternsResponse(patterns=[])
