from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv

IncidentSeverity = Literal["critical", "high", "medium", "low"]
IncidentStatus = Literal["open", "investigating", "resolved", "closed", "escalated"]
IncidentPriority = Literal["p1", "p2", "p3", "p4"]


class Incident(BaseModel):
    id: str = Field(..., description="Unique incident identifier")
    title: str = Field(..., description="Incident title or summary")
    description: str = Field(..., description="Detailed incident description")
    severity: IncidentSeverity = Field(..., description="Incident severity")
    status: IncidentStatus = Field(..., description="Current incident status")
    priority: IncidentPriority = Field(..., description="Incident priority")
    affected_envs: list[LiCloudEnv] = Field(..., description="Affected environments")
    created_at: datetime = Field(..., description="When the incident was created")
    updated_at: datetime = Field(..., description="When the incident was last updated")
    resolved_at: datetime | None = Field(None, description="When the incident was resolved")
    assignee: str | None = Field(None, description="Person assigned to the incident")
    tags: list[str] = Field(default_factory=list, description="Incident tags")
    related_alerts: list[str] = Field(default_factory=list, description="Related alert IDs")
    root_cause: str | None = Field(None, description="Root cause analysis")
    resolution: str | None = Field(None, description="Resolution details")


class SearchIncidentsRequest(BaseModel):
    env: LiCloudEnv | None = Field(None, description="Filter by affected environment")
    severity: IncidentSeverity | None = Field(None, description="Filter by severity")
    status: IncidentStatus | None = Field(None, description="Filter by status")
    priority: IncidentPriority | None = Field(None, description="Filter by priority")
    assignee: str | None = Field(None, description="Filter by assignee")
    limit: int = Field(default=50, description="Maximum number of incidents to return")
    days_back: int = Field(default=30, description="Number of days to look back")


def search_incidents(request: SearchIncidentsRequest) -> list[Incident]:
    """
    Retrieve incidents based on filters.

    Args:
        request (GetIncidentsRequest): Incident query parameters

    Returns:
        list[Incident]: List of incidents matching the criteria
    """
    # Placeholder for actual incident retrieval logic
    return []
