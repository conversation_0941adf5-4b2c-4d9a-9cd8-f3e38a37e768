"""
Service topology tool for retrieving and visualizing service topology data.

This tool provides service topology data with optional error statistics overlays
and can be used directly as a langchain StructuredTool.
"""

from copy import deepcopy
from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class NodeStatus(str, Enum):
    """Status of a topology node based on error statistics."""

    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class Node(BaseModel):
    """Represents a node in the service topology."""

    service_name: str = Field(..., description="Name of the service")
    node_type: str = Field(..., description="Type of the node (e.g., 'service', 'database', 'cache')")
    status: NodeStatus = Field(default=NodeStatus.UNKNOWN, description="Health status of the node")
    metadata: dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the node")

    # Error statistics (optional overlay information)
    alert_count: int = Field(default=0, description="Number of active alerts")
    error_log_count: int = Field(default=0, description="Number of error logs")
    error_trace_count: int = Field(default=0, description="Number of error traces")


class Edge(BaseModel):
    """Represents an edge/connection in the service topology."""

    source: str = Field(..., description="Source service name")
    target: str = Field(..., description="Target service name")
    relationship_type: str = Field(..., description="Type of relationship (e.g., 'calls', 'depends_on')")
    metadata: dict[str, Any] = Field(default_factory=dict, description="Additional metadata about the edge")


class ServiceTopology(BaseModel):
    """Complete service topology structure with flattened service nodes."""

    nodes: list[Node] = Field(..., description="List of all service nodes in the topology")
    edges: list[Edge] = Field(..., description="List of edges between services")
    metadata: dict[str, Any] = Field(default_factory=dict, description="Additional topology metadata")


# Mock data for demonstration - flattened service topology
_MOCK_TOPOLOGY_DATA: dict[str, Any] = {
    "nodes": [
        Node(
            service_name="user-service",
            node_type="service",
            status=NodeStatus.HEALTHY,
            alert_count=0,
            error_log_count=2,
            error_trace_count=0,
        ),
        Node(
            service_name="auth-service",
            node_type="service",
            status=NodeStatus.HEALTHY,
            alert_count=0,
            error_log_count=0,
            error_trace_count=0,
        ),
        Node(
            service_name="order-service",
            node_type="service",
            status=NodeStatus.HEALTHY,
            alert_count=0,
            error_log_count=1,
            error_trace_count=0,
        ),
        Node(
            service_name="payment-service",
            node_type="service",
            status=NodeStatus.CRITICAL,
            alert_count=5,
            error_log_count=25,
            error_trace_count=8,
        ),
        Node(
            service_name="notification-service",
            node_type="service",
            status=NodeStatus.WARNING,
            alert_count=2,
            error_log_count=10,
            error_trace_count=1,
        ),
    ],
    "edges": [
        Edge(source="user-service", target="auth-service", relationship_type="calls"),
        Edge(source="order-service", target="user-service", relationship_type="calls"),
        Edge(source="order-service", target="payment-service", relationship_type="calls"),
        Edge(source="order-service", target="notification-service", relationship_type="calls"),
        Edge(source="payment-service", target="notification-service", relationship_type="calls"),
    ],
}


async def get_service_topology(
    service_name: str | None = None,
    depth: int = 3,
    include_error_overlay: bool = False,
) -> str:
    """
    Retrieve service topology data including all service nodes and their relationships,
    and optionally support error overlays.

    Args:
        service_name: Optional filter for a specific service (if None, returns full topology)
        depth: Maximum depth of the topology graph (default: 3, currently unused in flattened structure)
        include_error_overlay: Whether to include error statistics overlay

    Returns:
        str: JSON representation of the service topology
    """
    try:
        nodes: list[Node] = deepcopy(_MOCK_TOPOLOGY_DATA["nodes"])
        edges: list[Edge] = deepcopy(_MOCK_TOPOLOGY_DATA["edges"])

        # Filter nodes and edges if specific service is requested
        if service_name:
            # Get all connected services within depth
            connected_services = _get_connected_services(service_name, edges, depth)
            connected_services.add(service_name)

            # Filter nodes to only include connected services
            nodes = [node for node in nodes if node.service_name in connected_services]

            # Filter edges to only include connections between filtered nodes
            service_names = {node.service_name for node in nodes}
            edges = [edge for edge in edges if edge.source in service_names and edge.target in service_names]

        # Apply error overlay if requested
        if include_error_overlay:
            nodes = _apply_error_overlay(nodes)

        topology = ServiceTopology(
            nodes=nodes,
            edges=edges,
            metadata={
                "depth": depth,
                "generated_by": "mock_topology_tool",
                "service_filter": service_name,
                "total_services": len(nodes),
            },
        )

        return topology.model_dump_json(indent=2)
    except Exception as e:
        return f"Error retrieving topology: {str(e)}"


def _get_connected_services(service_name: str, edges: list[Edge], depth: int) -> set[str]:
    """Get all services connected to the given service within the specified depth."""
    connected: set[str] = set()
    current_level = {service_name}

    for _ in range(depth):
        next_level = set()
        for service in current_level:
            for edge in edges:
                if edge.source == service:
                    next_level.add(edge.target)
                elif edge.target == service:
                    next_level.add(edge.source)

        # Remove already processed services
        next_level -= connected
        next_level -= current_level

        if not next_level:
            break

        connected.update(next_level)
        current_level = next_level

    return connected


def _apply_error_overlay(nodes: list[Node]) -> list[Node]:
    """Apply error statistics overlay to determine node status."""
    for node in nodes:
        # Update node status based on error counts
        total_errors = node.alert_count + node.error_log_count + node.error_trace_count

        # Simple logic to determine status based on error counts
        if total_errors == 0:
            node.status = NodeStatus.HEALTHY
        elif total_errors < 20:
            node.status = NodeStatus.WARNING
        else:
            node.status = NodeStatus.CRITICAL

    return nodes
