from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class Trace(BaseModel):
    """A trace summary with basic information"""

    trace_id: str = Field(..., description="Unique trace identifier")
    root_span_name: str = Field(..., description="Name of the root span/operation")
    duration_ms: float = Field(..., description="Total trace duration in milliseconds")
    status: Literal["ok", "error", "timeout"] = Field(..., description="Trace status")
    error_count: int = Field(..., description="Number of errors in this trace")
    span_count: int = Field(..., description="Total number of spans in this trace")


class Span(BaseModel):
    """A single span within a trace"""

    span_id: str = Field(..., description="Unique span identifier")
    parent_span_id: str | None = Field(None, description="Parent span ID, null for root span")
    operation_name: str = Field(..., description="Name of the operation/span")
    service_name: str = Field(..., description="Service that owns this span")
    start_time: datetime = Field(..., description="When the span started")
    duration_ms: float = Field(..., description="Span duration in milliseconds")
    status: Literal["ok", "error", "timeout"] = Field(..., description="Span status")
    tags: dict[str, str] = Field(default_factory=dict, description="Span tags/attributes")


class SpanTree(BaseModel):
    """A span with its children organized in tree structure"""

    span: Span = Field(..., description="The span data")
    children: list["SpanTree"] = Field(default_factory=list, description="Child spans")


class SearchTracesRequest(BaseModel):
    """Request to search traces for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    start_time: datetime = Field(..., description="Start time for trace search")
    end_time: datetime = Field(..., description="End time for trace search")
    min_duration_ms: float | None = Field(None, description="Minimum trace duration in ms")
    max_duration_ms: float | None = Field(None, description="Maximum trace duration in ms")
    has_errors: bool | None = Field(None, description="Filter traces with/without errors")
    span_name: str | None = Field(None, description="Filter by span name (partial match)")
    limit: int = Field(default=100, description="Maximum number of traces to return")


class GetTraceTreeRequest(BaseModel):
    """Request to get the tree structure of a specific trace"""

    trace_id: str = Field(..., description="Trace ID to retrieve")


def search_traces(request: SearchTracesRequest) -> list[Trace]:
    """
    Search traces for a component with filtering support.

    Supports filtering by latency (duration), error status, and span names
    to help identify problematic traces or specific operations.

    Args:
        request: Search parameters for traces

    Returns:
        List of traces matching the search criteria
    """
    # Mock data representing various traces
    all_traces = [
        Trace(
            trace_id="trace-001",
            root_span_name=f"{request.component}-api-call",
            duration_ms=1250.0,
            status="error",
            error_count=1,
            span_count=5,
        ),
        Trace(
            trace_id="trace-002",
            root_span_name=f"{request.component}-db-query",
            duration_ms=850.0,
            status="ok",
            error_count=0,
            span_count=3,
        ),
        Trace(
            trace_id="trace-003",
            root_span_name=f"{request.component}-health-check",
            duration_ms=50.0,
            status="ok",
            error_count=0,
            span_count=2,
        ),
        Trace(
            trace_id="trace-004",
            root_span_name=f"{request.component}-user-request",
            duration_ms=2100.0,
            status="timeout",
            error_count=0,
            span_count=8,
        ),
        Trace(
            trace_id="trace-005",
            root_span_name=f"{request.component}-cache-operation",
            duration_ms=25.0,
            status="ok",
            error_count=0,
            span_count=1,
        ),
        Trace(
            trace_id="trace-006",
            root_span_name=f"{request.component}-batch-process",
            duration_ms=5000.0,
            status="error",
            error_count=2,
            span_count=12,
        ),
    ]

    # Apply filters
    filtered_traces = []
    for trace in all_traces:
        # Filter by duration
        if request.min_duration_ms and trace.duration_ms < request.min_duration_ms:
            continue
        if request.max_duration_ms and trace.duration_ms > request.max_duration_ms:
            continue

        # Filter by error status
        if request.has_errors is not None:
            if request.has_errors and trace.error_count == 0:
                continue
            if not request.has_errors and trace.error_count > 0:
                continue

        # Filter by span name (partial match)
        if request.span_name and request.span_name.lower() not in trace.root_span_name.lower():
            continue

        filtered_traces.append(trace)

    # Apply limit
    return filtered_traces[: request.limit]


def get_trace_tree(request: GetTraceTreeRequest) -> SpanTree | None:
    """
    Get the tree structure of spans for a specific trace.

    Returns all spans in the trace organized as a tree that shows
    the parent-child relationships and timing information.

    Args:
        request: Request with trace ID to retrieve

    Returns:
        Root span with nested children, or None if trace not found
    """
    # Get all spans for the trace (flat list)
    spans = _get_spans_for_trace(request.trace_id)
    if not spans:
        return None

    # Build tree structure
    return _build_span_tree(spans)


def _get_spans_for_trace(trace_id: str) -> list[Span]:
    """Get flat list of spans for a trace"""
    if trace_id == "trace-001":
        # API call trace with error
        return [
            Span(
                span_id="span-001-1",
                parent_span_id=None,
                operation_name="user-request",
                service_name="api-gateway",
                start_time=datetime(2025, 8, 14, 10, 0, 0),
                duration_ms=1250.0,
                status="error",
                tags={"http.method": "POST", "http.status_code": "500"},
            ),
            Span(
                span_id="span-001-2",
                parent_span_id="span-001-1",
                operation_name="validate-request",
                service_name="api-gateway",
                start_time=datetime(2025, 8, 14, 10, 0, 0, 50000),
                duration_ms=50.0,
                status="ok",
                tags={"validation": "passed"},
            ),
            Span(
                span_id="span-001-3",
                parent_span_id="span-001-1",
                operation_name="database-query",
                service_name="user-service",
                start_time=datetime(2025, 8, 14, 10, 0, 0, 100000),
                duration_ms=800.0,
                status="error",
                tags={"db.statement": "SELECT * FROM users", "error": "connection timeout"},
            ),
            Span(
                span_id="span-001-4",
                parent_span_id="span-001-3",  # Child of database-query
                operation_name="retry-connection",
                service_name="user-service",
                start_time=datetime(2025, 8, 14, 10, 0, 0, 500000),
                duration_ms=300.0,
                status="error",
                tags={"retry.attempt": "1"},
            ),
            Span(
                span_id="span-001-5",
                parent_span_id="span-001-1",
                operation_name="cache-lookup",
                service_name="user-service",
                start_time=datetime(2025, 8, 14, 10, 0, 0, 900000),
                duration_ms=20.0,
                status="ok",
                tags={"cache.hit": "false"},
            ),
            Span(
                span_id="span-001-6",
                parent_span_id="span-001-1",
                operation_name="error-handling",
                service_name="api-gateway",
                start_time=datetime(2025, 8, 14, 10, 0, 1, 0),
                duration_ms=100.0,
                status="ok",
                tags={"error.type": "DatabaseError"},
            ),
        ]
    elif trace_id == "trace-002":
        # Simple successful DB query with nested spans
        return [
            Span(
                span_id="span-002-1",
                parent_span_id=None,
                operation_name="db-query",
                service_name="data-service",
                start_time=datetime(2025, 8, 14, 10, 1, 0),
                duration_ms=850.0,
                status="ok",
                tags={"db.statement": "SELECT * FROM products"},
            ),
            Span(
                span_id="span-002-2",
                parent_span_id="span-002-1",
                operation_name="connection-pool",
                service_name="data-service",
                start_time=datetime(2025, 8, 14, 10, 1, 0, 10000),
                duration_ms=50.0,
                status="ok",
                tags={"pool.size": "10"},
            ),
            Span(
                span_id="span-002-3",
                parent_span_id="span-002-1",
                operation_name="execute-query",
                service_name="data-service",
                start_time=datetime(2025, 8, 14, 10, 1, 0, 60000),
                duration_ms=780.0,
                status="ok",
                tags={"rows.returned": "150"},
            ),
            Span(
                span_id="span-002-4",
                parent_span_id="span-002-3",  # Child of execute-query
                operation_name="result-parsing",
                service_name="data-service",
                start_time=datetime(2025, 8, 14, 10, 1, 0, 700000),
                duration_ms=140.0,
                status="ok",
                tags={"format": "json"},
            ),
        ]
    else:
        # Default minimal trace
        return [
            Span(
                span_id=f"span-{trace_id}-1",
                parent_span_id=None,
                operation_name="unknown-operation",
                service_name="unknown-service",
                start_time=datetime(2025, 8, 14, 10, 0, 0),
                duration_ms=100.0,
                status="ok",
                tags={},
            )
        ]


def _build_span_tree(spans: list[Span]) -> SpanTree | None:
    """Build tree structure from flat list of spans"""
    if not spans:
        return None

    # Create a map of span_id -> span for quick lookup
    span_map = {span.span_id: span for span in spans}

    # Find root span (no parent)
    root_span = None
    for span in spans:
        if span.parent_span_id is None:
            root_span = span
            break

    if not root_span:
        return None

    # Recursively build tree starting from root
    return _build_span_tree_recursive(root_span, span_map)


def _build_span_tree_recursive(span: Span, span_map: dict[str, Span]) -> SpanTree:
    """Recursively build span tree"""
    # Find all children of current span
    children = []
    for other_span in span_map.values():
        if other_span.parent_span_id == span.span_id:
            child_tree = _build_span_tree_recursive(other_span, span_map)
            children.append(child_tree)

    # Sort children by start time
    children.sort(key=lambda x: x.span.start_time)

    return SpanTree(span=span, children=children)
