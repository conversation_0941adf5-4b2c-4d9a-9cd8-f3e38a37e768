from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class Ticket(BaseModel):
    id: str = Field(..., description="Unique ticket identifier")
    title: str = Field(..., description="Ticket title or summary")
    description: str = Field(..., description="Detailed ticket description")
    type: Literal["bug", "feature", "task", "improvement", "support"] = Field(..., description="Ticket type")
    status: Literal["open", "in_progress", "resolved", "closed", "blocked", "pending"] = Field(
        ..., description="Current ticket status"
    )
    priority: Literal["critical", "high", "medium", "low"] = Field(..., description="Ticket priority")
    severity: Literal["critical", "major", "minor", "trivial"] = Field(..., description="Ticket severity")
    env: LiCloudEnv = Field(..., description="Environment affected")
    assignee: str | None = Field(None, description="Person assigned to the ticket")
    reporter: str = Field(..., description="Person who reported the ticket")
    created_at: datetime = Field(..., description="When the ticket was created")
    updated_at: datetime = Field(..., description="When the ticket was last updated")
    resolved_at: datetime | None = Field(None, description="When the ticket was resolved")
    labels: list[str] = Field(default_factory=list, description="Ticket labels/tags")
    related_incidents: list[str] = Field(default_factory=list, description="Related incident IDs")
    attachments: list[str] = Field(default_factory=list, description="File attachments")


class CreateTicketRequest(BaseModel):
    title: str = Field(..., description="Ticket title")
    description: str = Field(..., description="Ticket description")
    type: Literal["bug", "feature", "task", "improvement", "support"] = Field(..., description="Ticket type")
    priority: Literal["critical", "high", "medium", "low"] = Field(..., description="Ticket priority")
    severity: Literal["critical", "major", "minor", "trivial"] = Field(..., description="Ticket severity")
    env: LiCloudEnv = Field(..., description="Environment")
    assignee: str | None = Field(None, description="Person to assign the ticket to")
    labels: list[str] = Field(default_factory=list, description="Ticket labels")


def create_ticket(request: CreateTicketRequest) -> Ticket:
    """
    Create a new ticket.

    Args:
        request (CreateTicketRequest): Ticket creation parameters

    Returns:
        Ticket: The created ticket
    """
    # Placeholder for actual ticket creation logic
    return Ticket(
        id="placeholder",
        title=request.title,
        description=request.description,
        type=request.type,
        status="open",
        priority=request.priority,
        severity=request.severity,
        env=request.env,
        assignee=request.assignee,
        reporter="system",
        created_at=datetime.now(),
        updated_at=datetime.now(),
        resolved_at=None,
        labels=request.labels,
        related_incidents=[],
        attachments=[],
    )


class SearchTicketsRequest(BaseModel):
    query: str = Field(
        ...,
        description="Search query string to filter tickets, allows for semantic search",
    )
    type: Literal["bug", "feature", "task", "improvement", "support"] | None = Field(
        None, description="Filter by ticket type"
    )
    status: Literal["open", "in_progress", "resolved", "closed", "blocked", "pending"] | None = Field(
        None, description="Filter by ticket status"
    )
    limit: int = Field(default=50, description="Maximum number of tickets to return")


def search_tickets(request: SearchTicketsRequest) -> list[Ticket]:
    """
    Search for tickets based on a query string.

    Args:
        request (SearchTicketsRequest): Ticket search parameters

    Returns:
        list[Ticket]: List of tickets matching the search criteria
    """
    # Placeholder for actual ticket search logic
    return []
