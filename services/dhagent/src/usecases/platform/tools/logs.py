from datetime import datetime

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class Log(BaseModel):
    """A single log entry"""

    timestamp: datetime = Field(..., description="When the log entry was created")
    level: str = Field(..., description="Log level (DEBUG, INFO, WARN, ERROR, etc.)")
    message: str = Field(..., description="Log message content")
    source: str = Field(..., description="Source of the log (application, system, deployment, etc.)")


class GetLogsRequest(BaseModel):
    """Request to get logs for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    start_time: datetime = Field(..., description="Start time for log collection")
    end_time: datetime = Field(..., description="End time for log collection")
    keyword: str | None = Field(None, description="Keyword to filter log messages")


def get_logs(request: GetLogsRequest) -> list[Log]:
    """
    Get logs for a component with filtering support.

    Retrieves log entries for the specified component within the given time range.
    Supports filtering by keyword to find specific log messages.

    Args:
        request: Request parameters for log retrieval

    Returns:
        List of log entries matching the criteria
    """
    # Mock data representing typical logs around deployment time
    all_logs = [
        Log(
            timestamp=request.start_time,
            level="INFO",
            message=f"Starting deployment of {request.component} v2.1.0",
            source="deployment",
        ),
        Log(
            timestamp=request.start_time,
            level="ERROR",
            message=f"Failed to connect to database for component {request.component}",
            source="application",
        ),
        Log(
            timestamp=request.start_time,
            level="WARN",
            message=f"High memory usage detected in {request.component}",
            source="system",
        ),
        Log(
            timestamp=request.start_time,
            level="ERROR",
            message=f"HTTP 503 Service Unavailable returned by {request.component}",
            source="application",
        ),
        Log(
            timestamp=request.start_time,
            level="INFO",
            message=f"Configuration updated for {request.component}",
            source="system",
        ),
        Log(
            timestamp=request.end_time,
            level="INFO",
            message=f"Database connection restored for {request.component}",
            source="application",
        ),
        Log(
            timestamp=request.end_time,
            level="INFO",
            message=f"Component {request.component} deployment completed successfully",
            source="deployment",
        ),
        Log(
            timestamp=request.end_time,
            level="INFO",
            message=f"Health check passed for {request.component}",
            source="system",
        ),
        Log(
            timestamp=request.end_time,
            level="DEBUG",
            message=f"Memory usage normalized for {request.component}",
            source="system",
        ),
    ]

    # Filter by keyword if provided
    if request.keyword:
        filtered_logs = []
        keyword_lower = request.keyword.lower()
        for log in all_logs:
            if keyword_lower in log.message.lower():
                filtered_logs.append(log)
        return filtered_logs

    return all_logs
