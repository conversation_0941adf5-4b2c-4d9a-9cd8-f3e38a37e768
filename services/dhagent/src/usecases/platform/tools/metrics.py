import math
import random
from datetime import datetime, timedelta
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class Metric(BaseModel):
    """Information about an available metric"""

    name: str = Field(..., description="Metric name (e.g., 'cpu_usage', 'request_rate')")
    description: str = Field(..., description="Human-readable description of the metric")
    unit: str = Field(..., description="Unit of measurement (percent, ms, bytes/sec, etc.)")
    category: Literal["golden_signal", "infrastructure", "custom"] = Field(..., description="Metric category")


class ListMetricsRequest(BaseModel):
    """Request to list available metrics for a component"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    category: Literal["golden_signal", "infrastructure", "custom", "all"] = Field(
        default="all", description="Filter by metric category"
    )


class TimeSeriesDataPoint(BaseModel):
    """A single data point in a time series"""

    timestamp: datetime = Field(..., description="When the metric was recorded")
    value: float = Field(..., description="Metric value")


class GetTimeSeriesRequest(BaseModel):
    """Request to get time series data for a specific metric"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    metric_name: str = Field(..., description="Name of the metric to retrieve")
    start_time: datetime = Field(..., description="Start time for data collection")
    end_time: datetime = Field(..., description="End time for data collection")
    interval: str = Field(default="1m", description="Data aggregation interval (1m, 5m, 15m, 1h)")


def list_metrics(request: ListMetricsRequest) -> list[Metric]:
    """
    List available metrics for a component.

    Different components may have different available metrics depending on their
    technology stack and instrumentation. Metrics are categorized by type:
    - golden_signal: Application performance indicators (latency, traffic, errors, saturation)
    - infrastructure: System resource metrics (CPU, memory, disk, network)
    - custom: Component-specific business metrics

    Args:
        request: Request parameters for listing metrics

    Returns:
        List of available metrics with their metadata
    """
    # Mock data representing typical metrics available for a component
    all_metrics = [
        # Golden signal metrics - Application performance
        Metric(name="request_rate", description="HTTP requests per second", unit="req/sec", category="golden_signal"),
        Metric(name="error_rate", description="Error rate percentage", unit="percent", category="golden_signal"),
        Metric(
            name="response_time_p95", description="95th percentile response time", unit="ms", category="golden_signal"
        ),
        Metric(name="response_time_avg", description="Average response time", unit="ms", category="golden_signal"),
        Metric(
            name="active_connections",
            description="Number of active connections",
            unit="count",
            category="golden_signal",
        ),
        # Infrastructure metrics - System resources
        Metric(name="cpu_usage", description="CPU utilization percentage", unit="percent", category="infrastructure"),
        Metric(
            name="memory_usage", description="Memory utilization percentage", unit="percent", category="infrastructure"
        ),
        Metric(name="disk_io", description="Disk I/O operations per second", unit="iops", category="infrastructure"),
        Metric(name="network_rx", description="Network receive rate", unit="bytes/sec", category="infrastructure"),
        Metric(name="network_tx", description="Network transmit rate", unit="bytes/sec", category="infrastructure"),
    ]

    # Filter by category if specified
    if request.category != "all":
        all_metrics = [m for m in all_metrics if m.category == request.category]

    return all_metrics


def get_time_series(request: GetTimeSeriesRequest) -> list[TimeSeriesDataPoint]:
    """
    Get time series data points for a specific metric.

    Returns historical data points for the specified metric within the given
    time range. Data is aggregated according to the specified interval.

    Args:
        request: Request parameters for time series data

    Returns:
        List of time series data points ordered by timestamp
    """
    # Mock data - generate realistic time series based on metric type
    data_points = []
    current_time = request.start_time

    # Generate data points every minute (simplified)
    while current_time <= request.end_time:
        value = _generate_mock_metric_value(request.metric_name, current_time)
        data_points.append(TimeSeriesDataPoint(timestamp=current_time, value=value))
        current_time += timedelta(minutes=1)

    return data_points


def _generate_mock_metric_value(metric_name: str, timestamp: datetime) -> float:
    """Generate realistic mock values based on metric type"""
    # Add some time-based variation to make data more realistic
    time_factor = math.sin(timestamp.hour * math.pi / 12)  # Daily cycle
    base_random = random.uniform(0.8, 1.2)

    # Configuration for different metrics
    metric_configs = {
        "cpu_usage": {"base": 45.0, "variation": 20, "min": 0, "max": 100},  # 25-65%
        "memory_usage": {"base": 60.0, "variation": 15, "min": 0, "max": 100},  # 45-75%
        "response_time_avg": {"base": 150.0, "variation": 100, "min": 10},  # 50-250ms
        "response_time_p95": {"base": 300.0, "variation": 200, "min": 20},  # 100-500ms
        "error_rate": {
            "base": 2.0,
            "variation": 3,
            "min": 0,
            "max": 100,
            "always_positive": True,
        },  # 0-5% with daily variation
        "request_rate": {"base": 100.0, "variation": 80, "min": 0},  # 20-180 req/sec
    }

    # Get metric configuration or use default
    config = metric_configs.get(metric_name, {})
    if not config:
        # Default generic value
        return random.uniform(0, 100)

    # Calculate base value
    variation_factor = config.get("variation", 0)
    if config.get("always_positive", False):
        base_value = config["base"] + max(0, time_factor * variation_factor)
    else:
        base_value = config["base"] + time_factor * variation_factor

    # Apply random factor and enforce bounds
    result = base_value * base_random
    result = max(config.get("min", 0), result)
    if "max" in config:
        result = min(config["max"], result)

    return result
