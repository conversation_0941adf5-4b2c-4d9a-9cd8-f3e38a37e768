from typing import Literal

from pydantic import BaseModel, Field


class RollbackComponentRequest(BaseModel):
    """Request to rollback an OAM component to a previous version using history ID"""

    history_id: int = Field(..., description="History ID to rollback to (from deployment history)")
    component_id: int = Field(..., description="OAM component ID to rollback")


class RollbackComponentResponse(BaseModel):
    """Response from a component rollback operation"""

    status: Literal["initiated", "in_progress", "completed", "failed", "cancelled"] = Field(
        ..., description="Rollback status"
    )
    message: str = Field(..., description="Status message or error details")


def rollback_component(request: RollbackComponentRequest) -> RollbackComponentResponse:
    """
    Rollback an OAM component to a specific deployment history.

    This function initiates a rollback of the specified component to a previous deployment
    state identified by the history ID. This matches the LiCloud OAM API endpoint:
    POST /licloud-oam-service/v1/deploy/rollback/{history_id}/components/{component_id}

    Args:
        request: Rollback parameters including history ID and component ID

    Returns:
        RollbackComponentResponse: Details about the initiated rollback operation

    """

    # Mock rollback response
    return RollbackComponentResponse(
        status="initiated",
        message=(f"Rollback initiated for component {request.component_id} to history {request.history_id}"),
    )
