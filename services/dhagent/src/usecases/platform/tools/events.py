from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field

from src.external.licloud.types import LiCloudEnv


class EventType(str, Enum):
    """Types of infrastructure and application events"""

    # Application events
    ROLLOUT = "rollout"
    ROLLBACK = "rollback"
    CONFIG_UPDATE = "config_update"
    RESTART = "restart"
    SCALE_UP = "scale_up"
    SCALE_DOWN = "scale_down"

    # Database events
    DATABASE_MIGRATION = "database_migration"
    DATABASE_FAILOVER = "database_failover"

    # Infrastructure events
    NETWORK_CHANGE = "network_change"
    CERTIFICATE_RENEWAL = "certificate_renewal"
    MAINTENANCE_WINDOW = "maintenance_window"

    # Monitoring events
    HEALTH_CHECK = "health_check"
    ALERT_TRIGGERED = "alert_triggered"
    INCIDENT_CREATED = "incident_created"


class Event(BaseModel):
    """An infrastructure or application event"""

    event_id: str = Field(..., description="Unique event identifier")
    timestamp: datetime = Field(..., description="When the event occurred")
    event_type: EventType = Field(..., description="Type of event")
    component: str = Field(..., description="Component affected by the event")
    source: str = Field(..., description="System or service that generated the event")
    message: str = Field(..., description="Detailed event description")
    severity: Literal["info", "warning", "error", "critical"] = Field(..., description="Event severity")
    metadata: dict[str, str] = Field(default_factory=dict, description="Additional event metadata")


class SearchEventsRequest(BaseModel):
    """Request to search for events"""

    component: str = Field(..., description="OAM component identifier")
    env: LiCloudEnv = Field(default="dev", description="Environment")
    start_time: datetime = Field(..., description="Start time for event search")
    end_time: datetime = Field(..., description="End time for event search")
    query: str | None = Field(None, description="Natural language query like 'any database events?' or 'rollbacks'")
    event_types: list[EventType] | None = Field(None, description="Filter by specific event types")
    severity: Literal["info", "warning", "error", "critical"] | None = Field(None, description="Filter by severity")
    limit: int = Field(default=100, description="Maximum number of events to return")


def search_events(request: SearchEventsRequest) -> list[Event]:
    """
    Search for infrastructure and application events.

    Supports natural language queries to find relevant events like deployments,
    database changes, network modifications, and other infrastructure events.

    Args:
        request: Search parameters for events

    Returns:
        List of events matching the search criteria
    """
    # Mock data representing various infrastructure and application events
    base_time = request.start_time
    all_events = [
        # Deployment events
        Event(
            event_id="event-001",
            timestamp=base_time + timedelta(minutes=5),
            event_type=EventType.ROLLOUT,
            component=request.component,
            source="deployment-controller",
            message=f"Started rolling deployment of {request.component} v2.1.0 to {request.env}",
            severity="info",
            metadata={"version": "v2.1.0", "replicas": "3", "strategy": "RollingUpdate"},
        ),
        Event(
            event_id="event-002",
            timestamp=base_time + timedelta(minutes=15),
            event_type=EventType.CONFIG_UPDATE,
            component=request.component,
            source="config-manager",
            message=f"Configuration updated for {request.component}: increased memory limit to 2Gi",
            severity="info",
            metadata={"config_key": "resources.memory", "old_value": "1Gi", "new_value": "2Gi"},
        ),
        Event(
            event_id="event-003",
            timestamp=base_time + timedelta(minutes=25),
            event_type=EventType.HEALTH_CHECK,
            component=request.component,
            source="health-monitor",
            message=f"Health check failed for {request.component}: readiness probe timeout",
            severity="warning",
            metadata={"probe_type": "readiness", "timeout": "30s", "endpoint": "/health"},
        ),
        Event(
            event_id="event-004",
            timestamp=base_time + timedelta(minutes=30),
            event_type=EventType.ROLLBACK,
            component=request.component,
            source="deployment-controller",
            message=f"Initiated rollback of {request.component} to previous version v2.0.8",
            severity="warning",
            metadata={"from_version": "v2.1.0", "to_version": "v2.0.8", "reason": "health_check_failure"},
        ),
        Event(
            event_id="event-005",
            timestamp=base_time + timedelta(minutes=45),
            event_type=EventType.DATABASE_MIGRATION,
            component=f"{request.component}-db",
            source="migration-service",
            message=f"Database schema migration completed for {request.component}: added user_preferences table",
            severity="info",
            metadata={"migration_id": "20250814_001", "tables_affected": "user_preferences", "duration": "2.3s"},
        ),
        Event(
            event_id="event-006",
            timestamp=base_time + timedelta(hours=1),
            event_type=EventType.DATABASE_FAILOVER,
            component=f"{request.component}-db",
            source="database-cluster",
            message=f"Database failover triggered for {request.component}: primary node became unresponsive",
            severity="error",
            metadata={"old_primary": "db-node-1", "new_primary": "db-node-2", "failover_time": "1.2s"},
        ),
        Event(
            event_id="event-007",
            timestamp=base_time + timedelta(hours=1, minutes=15),
            event_type=EventType.NETWORK_CHANGE,
            component="vpc-network",
            source="network-controller",
            message=f"Network security group updated for {request.component}: added ingress rule for port 8443",
            severity="info",
            metadata={"security_group": "sg-app-tier", "rule_type": "ingress", "port": "8443", "protocol": "TCP"},
        ),
        Event(
            event_id="event-008",
            timestamp=base_time + timedelta(hours=2),
            event_type=EventType.SCALE_UP,
            component=request.component,
            source="auto-scaler",
            message=f"Scaled up {request.component}: increased replicas from 3 to 5 due to high CPU usage",
            severity="info",
            metadata={"old_replicas": "3", "new_replicas": "5", "trigger": "cpu_usage", "threshold": "80%"},
        ),
        Event(
            event_id="event-009",
            timestamp=base_time + timedelta(hours=3),
            event_type=EventType.CERTIFICATE_RENEWAL,
            component=f"{request.component}-ingress",
            source="cert-manager",
            message=f"TLS certificate renewed for {request.component}: new certificate valid until 2026-02-14",
            severity="info",
            metadata={"cert_name": f"{request.component}-tls", "expiry": "2026-02-14", "issuer": "letsencrypt"},
        ),
        Event(
            event_id="event-010",
            timestamp=base_time + timedelta(hours=4),
            event_type=EventType.ALERT_TRIGGERED,
            component=request.component,
            source="monitoring-system",
            message=f"High error rate alert triggered for {request.component}: 15% of requests failing",
            severity="error",
            metadata={"alert_name": "HighErrorRate", "error_rate": "15%", "threshold": "5%", "duration": "5m"},
        ),
        Event(
            event_id="event-011",
            timestamp=base_time + timedelta(hours=5),
            event_type=EventType.RESTART,
            component=request.component,
            source="pod-controller",
            message=f"Pod restart triggered for {request.component}: container crashed with OOMKilled",
            severity="warning",
            metadata={
                "pod_name": f"{request.component}-deployment-abc123",
                "reason": "OOMKilled",
                "restart_count": "3",
            },
        ),
        Event(
            event_id="event-012",
            timestamp=base_time + timedelta(hours=6),
            event_type=EventType.MAINTENANCE_WINDOW,
            component="infrastructure",
            source="maintenance-scheduler",
            message=f"Maintenance window started for {request.env} environment: database cluster upgrade",
            severity="info",
            metadata={"maintenance_type": "database_upgrade", "duration": "2h", "impact": "minimal"},
        ),
    ]

    # Apply time range filter
    filtered_events = [event for event in all_events if request.start_time <= event.timestamp <= request.end_time]

    # Apply severity filter
    if request.severity:
        filtered_events = [event for event in filtered_events if event.severity == request.severity]

    # Apply event type filter
    if request.event_types:
        filtered_events = [event for event in filtered_events if event.event_type in request.event_types]

    # Apply natural language query filter
    if request.query:
        query_lower = request.query.lower()
        query_filtered = []

        for event in filtered_events:
            # Check if query matches event type, message, or metadata
            if (
                (
                    query_lower in event.event_type.value.lower()
                    or query_lower in event.message.lower()
                    or query_lower in event.source.lower()
                    or any(query_lower in str(value).lower() for value in event.metadata.values())
                )
                or "database" in query_lower
                and ("database" in event.event_type.value or "db" in event.component)
                or "network" in query_lower
                and event.event_type == EventType.NETWORK_CHANGE
                or "deploy" in query_lower
                and event.event_type in [EventType.ROLLOUT, EventType.ROLLBACK]
                or "error" in query_lower
                and event.severity in ["error", "critical"]
                or "scale" in query_lower
                and event.event_type in [EventType.SCALE_UP, EventType.SCALE_DOWN]
            ):
                query_filtered.append(event)

        filtered_events = query_filtered

    # Sort by timestamp (most recent first) and apply limit
    filtered_events.sort(key=lambda x: x.timestamp, reverse=True)
    return filtered_events[: request.limit]
