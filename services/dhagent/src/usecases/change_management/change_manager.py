import logging
import operator
import uuid
from enum import Enum
from typing import Any, cast

from langchain_core.messages import AIMessage, HumanMessage
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, MessagesState
from langgraph.graph.state import CompiledStateGraph, StateGraph
from langgraph.types import interrupt
from pydantic import BaseModel, Field

from src.config import settings
from src.entities.oam import History, OAMComponent
from src.usecases.shared import ComposableAgent

from .prompt_templates import (
    complete_todo_prompt,
    format_component_description,
    format_deploy_history,
    format_tool_descriptions,
    make_component_diagnostic_plan,
)
from .tool_manager import ToolManager

logger = logging.getLogger(__name__)


class TodoStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class TodoItem(BaseModel):
    """Represents a single todo item in the analysis plan"""

    description: str = Field(..., description="Description of the task to be performed")
    status: TodoStatus = Field(
        default=TodoStatus.PENDING,
        description=("Todo status"),
    )


class ComponentDiagnosticPlan(BaseModel):
    """Structured analysis plan with todo items"""

    todos: list[TodoItem] = Field(..., description="List of investigation tasks to perform")


class ChangeManagerState(MessagesState):
    component: OAMComponent
    history: History
    todos: list[TodoItem]  # plan to do list
    next_todo_index: int


class AgenticChangeManager(ComposableAgent):
    def __init__(self) -> None:
        self._chat_model = ChatOpenAI(
            model="azure-gpt-5-mini",
            base_url="http://api-hub.inner.chj.cloud/llm-gateway/v1",
            default_headers={
                "X-CHJ-GWToken": settings.apihub_token,
                "BCS-APIHub-RequestId": str(uuid.uuid1()),
            },
        )
        self._tool_manager = ToolManager()
        self._chat_model_with_tools = self._chat_model.bind_tools(self._tool_manager.list_tools())

        # 创建 checkpointer 用于状态持久化
        self._checkpointer = MemorySaver()
        self._graph: CompiledStateGraph = self._build_graph()

    def _build_graph(self) -> CompiledStateGraph:
        g = StateGraph(ChangeManagerState)

        g.add_node("create_plan", self._create_plan)
        g.add_node("execute_next_todo", self._execute_next_todo)

        g.add_edge(START, "create_plan")
        g.add_conditional_edges(
            "create_plan",
            self._should_continue,
            {
                True: "execute_next_todo",  # if todo remains, execute it
                False: END,  # else END
            },
        )
        g.add_edge("execute_next_todo", "create_plan")

        return g.compile(checkpointer=self._checkpointer)

    @property
    def graph(self) -> CompiledStateGraph:
        return self._graph

    def _should_continue(self, state: ChangeManagerState) -> bool:
        next_todo_index = state.get("next_todo_index", 0)
        return next_todo_index < len(state["todos"])

    async def _create_plan(self, state: ChangeManagerState) -> dict[str, Any]:
        """Create analysis plan based on the component information"""

        # if todos exists, skip
        if state.get("todos"):
            return {}

        component: OAMComponent = state["component"]
        history: History = state["history"]

        # Create a structured output model that knows about available tools
        planner = self._chat_model.with_structured_output(ComponentDiagnosticPlan)

        # Generate tool descriptions from available tools
        available_tools = self._tool_manager.list_tools()
        tool_descriptions = format_tool_descriptions(available_tools, numbered=False)

        # Generate component description
        component_description = format_component_description(component)

        # Generate deploy history description
        deploy_history_description = format_deploy_history(history)

        result = cast(
            "ComponentDiagnosticPlan",
            await planner.ainvoke(
                [
                    HumanMessage(
                        content=make_component_diagnostic_plan.format(
                            component_description=component_description,
                            deploy_history_description=deploy_history_description,
                            tool_descriptions=tool_descriptions,
                        )
                    )
                ]
            ),
        )

        return {
            "todos": result.todos,
            "messages": [
                HumanMessage(
                    content=(
                        f"{operator} just deployed an OAM Component {component.component_name},"
                        f"now we need to help diagnose it according to the following plan:\n"
                        f"{render_todos_as_markdown(result.todos)}"
                    )
                )
            ],
        }

    async def _execute_next_todo(self, state: ChangeManagerState) -> dict[str, Any]:
        """Execute next todo: LLM call + tool execution with HITL"""
        todos = state["todos"]
        next_todo_index = state.get("next_todo_index", 0)
        next_todo = todos[next_todo_index]

        # Generate tool descriptions from available tools
        available_tools = self._tool_manager.list_tools()
        tool_descriptions = format_tool_descriptions(available_tools, numbered=True)

        # Generate LLM response with tools
        prompt = complete_todo_prompt.format(todo=next_todo, tool_descriptions=tool_descriptions)
        ai_msg = cast("AIMessage", await self._chat_model_with_tools.ainvoke(prompt))

        # Immediately handle tool calls with interrupts
        result = []
        if hasattr(ai_msg, "tool_calls") and ai_msg.tool_calls:
            result = await self._handle_tool_calls_with_interrupt(ai_msg, state)
        else:
            # No tool calls, just return the AI message content
            result.append({"role": "assistant", "content": ai_msg.content})

        # Update todo status
        todos[next_todo_index].status = TodoStatus.COMPLETED

        return {
            "messages": result,
            "todos": todos,
            "next_todo_index": next_todo_index + 1,
        }

    async def _handle_tool_calls_with_interrupt(
        self, ai_message: AIMessage, state: ChangeManagerState
    ) -> list[dict[str, Any]]:
        """处理工具调用，支持中断逻辑"""

        result = []

        for tool_call in ai_message.tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call["args"]

            interruptible_tool = self._tool_manager.get_interruptible_tool(tool_name)
            if not interruptible_tool.is_sensitive:
                observation = interruptible_tool.tool.invoke(tool_args)
                result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
                continue

            # Create the interrupt request
            request = {
                "action_request": {"action": tool_call["name"], "args": tool_call["args"]},
                "config": interruptible_tool.config,
                "description": interruptible_tool.tool.description,
            }

            logger.info(f"HITL Tool call: {tool_call['name']} with args: {tool_call['args']}, request: {request}")

            response = interrupt([request])

            logger.info(f"User resume response: {response}, type: {response.type}, tool_call_id: {tool_call['id']}")

            # Handle the user response
            if response.type == "accept":
                # Execute the tool with original args
                observation = interruptible_tool.tool.invoke(tool_args)
                result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})

            elif response.type == "ignore":
                # Ignore this tool call and end the workflow
                result.append(
                    {
                        "role": "tool",
                        "content": f"User ignored {tool_name} and end the workflow.",
                        "tool_call_id": tool_call["id"],
                    }
                )
                state["next_todo_index"] = len(state["todos"])  # mark as completed
                break
            elif response.type == "edit":
                logger.info("implement edit logic here")
                pass
            elif response.type == "response":
                logger.info("implement response logic here")
                pass
            else:
                raise ValueError(f"Invalid tool call: {tool_call['name']}")

        return result


def render_todos_as_markdown(todos: list[TodoItem]) -> str:
    """Render the list of todo items as a Markdown string with checkboxes."""
    markdown = "# Diagnostic Plan Tasks\n\n"

    if not todos:
        markdown += "<Empty Plan>"
        return markdown

    markdown += "An array of tasks:\n\n"

    for todo in todos:
        # Use [x] for completed tasks, [ ] for pending/other statuses
        checkbox = "[x]" if todo.status == TodoStatus.COMPLETED else "[ ]"
        markdown += f"- {checkbox} {todo.description}\n"

    return markdown
