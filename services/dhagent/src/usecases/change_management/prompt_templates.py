"""
This file stores prompt templates for change_manager.
"""

from collections.abc import Sequence

from langchain_core.tools import StructuredTool

from src.entities.oam import History, OAMComponent


def format_tool_descriptions(tools: Sequence[StructuredTool], numbered: bool = False) -> str:
    """
    Generate formatted tool descriptions from a list of StructuredTool objects.

    Args:
        tools: List of LangChain StructuredTool objects
        numbered: If True, format tools with numbers (1. tool_name - description)
                  If False, format tools with bullets (- tool_name: description)

    Returns:
        Formatted string containing tool descriptions
    """
    if not tools:
        return "No tools available."

    formatted_tools = []
    for i, tool in enumerate(tools, 1):
        if numbered:
            formatted_tools.append(f"{i}. {tool.name} - {tool.description}")
        else:
            formatted_tools.append(f"- {tool.name}: {tool.description}")

    return "\n".join(formatted_tools)


def format_component_description(component: OAMComponent) -> str:
    """
    Generate formatted component description from an OAMComponent object.

    Args:
        component: OAMComponent object containing component information

    Returns:
        Formatted string containing component information
    """
    return f"""- ID: {component.id}
- Name: {component.component_name}
- Description: {component.component_desc}
- Type: {component.component_type}
- Language: {component.language}
- Status: {component.component_status}"""


def format_deploy_history(history: History) -> str:
    """
    Generate formatted deployment history description from a History object.

    Args:
        history: History object containing deployment information

    Returns:
        Formatted string containing deployment timing and details
    """
    # Format start and end times
    start_time = history.create_time.strftime("%Y-%m-%d %H:%M:%S UTC")
    end_time = history.update_time.strftime("%Y-%m-%d %H:%M:%S UTC")

    # Build the description with essential information only
    description_parts = [
        f"- Deployment Start: {start_time}",
        f"- Deployment End (Service Ready): {end_time}",
    ]

    if history.env:
        description_parts.append(f"- Environment: {history.env}")

    if history.vdc:
        description_parts.append(f"- Business Cluster: {history.vdc}")

    return "\n".join(description_parts)


make_component_diagnostic_plan = """
You are an expert DevOps engineer analyzing the health of an OAM component after a deployment or change.
This analysis is triggered by a deployment event, so you already know WHEN and WHAT was deployed.

Component Information:
{component_description}

Deploy History Information:
{deploy_history_description}

You have access to the following platform tools for investigation:
{tool_descriptions}

Create a structured analysis plan with sequential,
executable todos that establish causal relationship with the deployment:

INVESTIGATION SEQUENCE:
1. **Golden Signals Analysis**: Check component health through latency, traffic, errors, and saturation metrics
   comparing BEFORE and AFTER deployment
2. **Log Pattern Analysis**: Perform comprehensive log health check comparing patterns before vs after deployment
   to identify new errors or changes
3. **Trace Performance Analysis**: Check trace health comparing patterns before vs after deployment to identify
   performance regressions
4. **Topology Health Check**: Analyze service topology health to identify dependency issues or communication problems
5. **Remediation Planning**: If issues are found, consider rollback options or other remediation steps

CRITICAL: For health check analysis (golden signals, logs, traces, topology),
always compare PRE-deployment vs POST-deployment periods to establish causal relationship. Look for:
- New error patterns that didn't exist before deployment
- Performance degradations that started after deployment
- Changes in golden signal metrics (latency, traffic, errors, saturation)
- New failure modes or exceptions in logs
- Topology changes or dependency issues that emerged post-deployment

Create todos that are:
- **Comparative**: Always analyze before/after deployment periods using health check tools
- **Causal**: Focus on identifying changes that correlate with deployment timing
- **Time-bounded**: Specify both pre-deployment baseline and post-deployment investigation periods
- **Pattern-focused**: Look for NEW issues vs existing ones using comprehensive health checks
- **Actionable**: Clear about which health check tool to use and what comparative analysis to perform

Each todo should specify the tool (search_events, check_logs, check_golden_signals, check_traces, check_topology),
time periods (before/after deployment), and what comparative health analysis to perform.
"""

complete_todo_prompt = """
You are an expert DevOps engineer executing a specific diagnostic task for an OAM component analysis.

CURRENT TASK TO EXECUTE:
- Description: {todo.description}
- Status: {todo.status}
- Task Type: Diagnostic investigation step

CONTEXT:
This task is part of a systematic analysis plan to investigate the health and performance of a recently deployed
component. You need to execute this specific task using the available tools and provide detailed findings.

AVAILABLE TOOLS:
{tool_descriptions}

EXECUTION GUIDELINES:
1. **Read the task description carefully** and understand what specific investigation needs to be performed
2. **Choose the appropriate tool(s)** based on what the task is asking for
3. **Use proper time ranges** when specified in the task description (e.g., before/after deployment)
4. **Provide specific parameters** when calling tools (time ranges, component names, search keywords, etc.)
5. **Analyze the results** and extract meaningful insights related to the task objective
6. **Document your findings** with clear evidence from the tool outputs

ANALYSIS APPROACH:
- If the task mentions "before/after deployment", use comparative time ranges
- If the task asks for "correlation", look for temporal relationships in the data
- If the task focuses on "new patterns", identify changes from baseline behavior
- If the task requires "deep dive", use multiple tools in sequence for comprehensive analysis

RESPONSE FORMAT:
Provide a detailed response that includes:
1. **Tool Selection**: Which tool(s) you chose and why
2. **Parameters Used**: Specific parameters passed to the tools (time ranges, filters, etc.)
3. **Raw Findings**: Key data points and patterns discovered from tool outputs
4. **Analysis**: Interpretation of the findings in the context of the diagnostic task
5. **Recommendations**: Next steps or conclusions based on the analysis

Execute this task thoroughly and provide actionable insights for the component health assessment.
"""
