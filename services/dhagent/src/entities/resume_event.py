"""
用户恢复中断事件的数据模型

定义 user.resume.robot 事件的数据结构，基于 li-agentcore 事件格式
"""

from typing import Any, Literal

from pydantic import BaseModel, Field


class ResumeDataContent(BaseModel):
    """恢复事件的数据内容"""

    type: Literal["accept", "ignore", "response", "edit"] = Field(..., description="操作类型")
    action: str | None = Field(None, description="具体操作名称（当 type 为 accept 或 edit 时存在）")
    args: dict[str, Any] | None = Field(None, description="操作参数（当 type 为 accept 或 edit 时存在）")
    response: str | None = Field(None, description="用户补充的上下文信息（当 type 为 response 时存在）")


class ContentPart(BaseModel):
    """内容部分"""

    type: str = Field(..., description="内容类型，通常为 'data'")
    data: ResumeDataContent = Field(..., description="具体的恢复数据")


class EventContent(BaseModel):
    """事件内容"""

    parts: list[ContentPart] = Field(..., description="内容部分列表")


class EventMetadata(BaseModel):
    """事件元数据"""

    conversation_id: str = Field(..., description="对话ID, thread_id")
    user: str = Field(..., description="用户名")


class UserResumeEventData(BaseModel):
    """用户恢复中断事件数据（完整的 data 字段）"""

    metadata: EventMetadata = Field(..., description="事件元数据")
    content: EventContent = Field(..., description="事件内容")


# 示例事件数据
EXAMPLE_RESUME_EVENT_DATA = {
    "thread_id": "change_manager_1_demo-user",
    "user_response": {"action": "approve", "message": "用户批准执行工具"},
    "timestamp": "2025-01-21T10:30:00Z",
    "user_id": "demo-user",
}

EXAMPLE_MODIFY_EVENT_DATA = {
    "thread_id": "change_manager_1_demo-user",
    "user_response": {"action": "modify", "message": "修改参数后执行", "modified_args": {"limit": 50, "timeout": 30}},
    "timestamp": "2025-01-21T10:30:00Z",
    "user_id": "demo-user",
}

EXAMPLE_SKIP_EVENT_DATA = {
    "thread_id": "change_manager_1_demo-user",
    "user_response": {"action": "skip", "message": "跳过此工具"},
    "timestamp": "2025-01-21T10:30:00Z",
    "user_id": "demo-user",
}

EXAMPLE_ABORT_EVENT_DATA = {
    "thread_id": "change_manager_1_demo-user",
    "user_response": {"action": "abort", "message": "中止整个流程"},
    "timestamp": "2025-01-21T10:30:00Z",
    "user_id": "demo-user",
}
