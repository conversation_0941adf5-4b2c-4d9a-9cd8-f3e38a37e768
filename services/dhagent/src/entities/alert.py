"""Alert entity - Core business logic for alerts in SRE operations."""

import uuid
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class AlertSeverity(Enum):
    """Alert severity levels following standard SRE practices."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertStatus(Enum):
    """Alert status in its lifecycle."""

    FIRING = "firing"
    PENDING = "pending"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


class AlertSource(Enum):
    """Source systems that can generate alerts."""

    PROMETHEUS = "prometheus"
    GRAFANA = "grafana"
    KUBERNETES = "kubernetes"
    APPLICATION = "application"
    INFRASTRUCTURE = "infrastructure"
    CUSTOM = "custom"


class Alert(BaseModel):
    """
    Alert entity representing a monitoring alert in SRE operations.

    This entity contains the core data structure for alerts and follows
    clean architecture principles by being independent of external frameworks.
    """

    # Core identification
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    summary: str
    description: str | None = None

    # Alert classification
    severity: AlertSeverity = AlertSeverity.MEDIUM
    status: AlertStatus = AlertStatus.FIRING
    source: AlertSource = AlertSource.CUSTOM

    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    fired_at: datetime | None = None
    resolved_at: datetime | None = None

    # Metadata and labels
    labels: dict[str, str] = Field(default_factory=dict)
    annotations: dict[str, str] = Field(default_factory=dict)

    # Service context
    service: str | None = None
    environment: str | None = None

    # Additional context
    runbook_url: str | None = None
    dashboard_url: str | None = None

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}
