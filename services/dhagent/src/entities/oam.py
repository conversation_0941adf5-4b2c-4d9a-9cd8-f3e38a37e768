from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field


class OAMApplication(BaseModel):
    id: int
    tenant_id: str = Field(..., alias="tenantId")
    app_desc: str = Field(..., alias="appDesc")
    app_name: str = Field(..., alias="appName")
    app_name_cn: str = Field(..., alias="appNameCn")
    app_status: int = Field(..., alias="appStatus")  # TODO: use Enum when necessary @zhenghe
    creator: str | None = Field(default=None, alias="creator")
    owner: str = Field(..., description="owner's LDAP name")
    create_time: datetime = Field(..., alias="createTime")
    update_time: datetime = Field(..., alias="updateTime")


class Artifact(BaseModel):
    id: int
    app_id: int = Field(..., alias="appId")
    artifact: str = Field(..., alias="artifact")
    branch: str = Field(..., alias="branch")
    code_quality_scan_result: Any | None = Field(default=None, alias="codeQualityScanResult")
    code_security_level: Any | None = Field(default=None, alias="codeSecurityLevel")
    commit_id: str = Field(..., alias="commitId")
    commit_msg: str = Field(..., alias="commitMsg")
    commit_time: datetime | None = Field(default=None, alias="commitTime")
    committer: str | None = Field(default=None, alias="committer")
    component_id: int = Field(..., alias="componentId")
    create_time: datetime = Field(..., alias="createTime")
    creator: str = Field(..., alias="creator")
    from_workflow_name: str = Field(..., alias="fromWorkflowName")
    image: str = Field(..., alias="image")
    image_create_time: datetime = Field(..., alias="imageCreateTime")
    image_creator: str = Field(..., alias="imageCreator")
    image_security_scan_job_result: Any | None = Field(default=None, alias="imageSecurityScanJobResult")
    image_tag: str = Field(..., alias="imageTag")
    is_chosen: bool = Field(..., alias="isChosen")
    is_deleted: bool | None = Field(default=None, alias="isDeleted")
    pipeline_id: int = Field(..., alias="pipelineId")
    prod_image: str | None = Field(default=None, alias="prodImage")
    update_time: datetime = Field(..., alias="updateTime")
    version: str = Field(..., alias="version")


class OAMComponent(BaseModel):
    id: int
    app_id: int = Field(..., alias="appId")
    artifact: Artifact
    artifact_key: str = Field(..., alias="artifactKey")
    classify: Any | None = Field(default=None, alias="classify")
    component_desc: str = Field(..., alias="componentDesc")
    component_level: int = Field(..., alias="componentLevel")
    component_name: str = Field(..., alias="componentName")
    component_name_cn: str = Field(..., alias="componentNameCn")
    component_status: int = Field(..., alias="componentStatus")
    component_type: str = Field(..., alias="componentType")
    component_type_name: Any | None = Field(default=None, alias="componentTypeName")
    create_time: datetime = Field(..., alias="createTime")
    creator: str = Field(..., alias="creator")
    deploy_type: Any | None = Field(default=None, alias="deployType")
    language: str = Field(..., alias="language")
    update_time: datetime = Field(..., alias="updateTime")
    vehicle_cost: int = Field(..., alias="vehicleCost")


class ComponentData(BaseModel):
    cmd: str | None = Field(default=None, alias="cmd")
    cpu: str | None = Field(default=None, alias="cpu")
    memory: str | None = Field(default=None, alias="memory")
    port: str | None = Field(default=None, alias="port")
    process_param: str | None = Field(default=None, alias="processParam")
    program_param: str | None = Field(default=None, alias="programParam")
    replicas: int | None = Field(default=None, alias="replicas")


class Trait(BaseModel):
    trait_data: dict = Field(..., alias="traitData")
    trait_name: str = Field(..., alias="traitName")
    version: str | None = Field(default=None)


class DeployParam(BaseModel):
    component_data: ComponentData = Field(..., alias="componentData")
    component_name: str = Field(..., alias="componentName")
    traits: list[Trait]
    version: str | None = Field(default=None)


class HistoryData(BaseModel):
    deploy_param: list[DeployParam] = Field(..., alias="deployParam")


class Job(BaseModel):
    id: int
    branch: str | None = Field(default=None)
    create_time: datetime = Field(..., alias="createTime")
    creator: str
    data: Any | None = Field(default=None, alias="data")
    domain: str
    env: str
    is_auto: bool = Field(..., alias="isAuto")
    is_deleted: bool = Field(..., alias="isDeleted")
    is_pre_success: bool | None = Field(default=None, alias="isPreSuccess")
    job_label: str | None = Field(default=None, alias="jobLabel")
    job_name: str = Field(..., alias="jobName")
    job_type: str = Field(..., alias="jobType")
    pre_job_id: int | None = Field(default=None, alias="preJobId")
    rely_job_id: int | None = Field(default=None, alias="relyJobId")
    stage_name: str = Field(..., alias="stageName")
    template_id: int = Field(..., alias="templateId")
    update_time: datetime = Field(..., alias="updateTime")
    vdc: str | None
    vdc_desc: str | None = Field(default=None, alias="vdcDesc")
    vdc_name: str | None = Field(default=None, alias="vdcName")


class Stage(BaseModel):
    jobs: list[list[Job]]
    stage_name: str = Field(..., alias="stageName")


class Template(BaseModel):
    stages: list[Stage]
    template_id: int = Field(..., alias="templateId")


class History(BaseModel):
    id: int
    app_id: int = Field(..., alias="appId")
    app_version: str = Field(..., alias="appVersion")
    batch_id: int = Field(..., alias="batchId")
    component_id: int = Field(..., alias="componentId")
    create_time: datetime = Field(..., alias="createTime")
    data: HistoryData
    deploy_msg: Any | None = Field(default=None, alias="deployMsg")
    deploy_status: int = Field(..., alias="deployStatus")
    domain: str | None = Field(default=None, alias="domain")
    duration: int | None = Field(default=None, alias="duration")
    env: str | None = Field(default=None, alias="env")
    exec_type: str = Field(..., alias="execType")
    is_auto_next: bool = Field(..., alias="isAutoNext")
    is_transfer: int | None = Field(default=None, alias="isTransfer")
    job_id: int = Field(..., alias="jobId")
    job_name: str = Field(..., alias="jobName")
    job_type: str = Field(..., alias="jobType")
    operator: str | None = Field(default=None, alias="operator")
    pipeline_id: int = Field(..., alias="pipelineId")
    reference_commit_id: str = Field(..., alias="referenceCommitId")
    reference_name: str = Field(..., alias="referenceName")
    runtime_status: int = Field(..., alias="runtimeStatus")
    stage_name: str = Field(..., alias="stageName")
    template: Template
    update_time: datetime = Field(..., alias="updateTime")
    vdc: str | None = Field(default=None, alias="vdc")
    yaml: str | None = Field(default=None, alias="yaml")


class OAMAppDeployRunningSucceedData(BaseModel):
    app: OAMApplication
    components: list[OAMComponent]
    history: History
    reason: Any | None = Field(default=None, alias="reason")


class OAMAppDeployRunningSucceedEventData(BaseModel):
    data: OAMAppDeployRunningSucceedData
    operator: str | None = Field(default=None, alias="operator")
