"""Pytest configuration and fixtures."""

import asyncio
from collections.abc import Generator

import pytest
from fastapi.testclient import TestClient

from src.entities.alert import Alert, AlertSeverity
from src.interfaces.api.app import app


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def sample_alert() -> Alert:
    """Create a sample Alert for testing."""
    return Alert(
        id="test-alert-123",
        name="Sample Test Alert",
        summary="This is a sample alert for testing purposes",
        severity=AlertSeverity.MEDIUM,
        service="test-service",
        environment="test",
    )


@pytest.fixture
def sample_alert_data() -> dict:
    """Create sample alert data for API testing."""
    return {
        "name": "API Test Alert",
        "summary": "Alert data for API testing",
        "severity": "high",
        "service": "api-service",
        "environment": "staging",
    }
