"""Unit tests for Alert entity."""

from src.entities.alert import <PERSON><PERSON>, AlertSeverity


class TestAlert:
    """Test cases for the Alert entity."""

    def test_alert_creation(self):
        """Test creating an Alert with basic properties."""
        alert = Alert(
            id="test-123",
            name="Test Alert",
            summary="This is a test alert",
            severity=AlertSeverity.HIGH,
        )

        assert alert.id == "test-123"
        assert alert.name == "Test Alert"
        assert alert.summary == "This is a test alert"
        assert alert.severity == AlertSeverity.HIGH.value  # Pydantic converts enum to string
        assert alert.service is None
        assert alert.environment is None

    def test_alert_with_optional_fields(self):
        """Test creating an Alert with all fields."""
        alert = Alert(
            id="test-456",
            name="Database Alert",
            summary="Database connection issue",
            severity=AlertSeverity.CRITICAL,
            service="user-service",
            environment="production",
        )

        assert alert.id == "test-456"
        assert alert.name == "Database Alert"
        assert alert.summary == "Database connection issue"
        assert alert.severity == AlertSeverity.CRITICAL.value  # Pydantic converts enum to string
        assert alert.service == "user-service"
        assert alert.environment == "production"

    def test_alert_severity_enum(self):
        """Test AlertSeverity enum values."""
        assert AlertSeverity.LOW.value == "low"
        assert AlertSeverity.MEDIUM.value == "medium"
        assert AlertSeverity.HIGH.value == "high"
        assert AlertSeverity.CRITICAL.value == "critical"

    def test_alert_equality(self):
        """Test Alert equality based on id."""
        alert1 = Alert(
            id="same-id",
            name="Alert 1",
            summary="First alert",
            severity=AlertSeverity.LOW,
        )

        alert2 = Alert(
            id="same-id",
            name="Alert 2",
            summary="Second alert",
            severity=AlertSeverity.HIGH,
        )

        # Alerts with same ID should be considered equal
        assert alert1.id == alert2.id

    def test_alert_string_representation(self):
        """Test string representation of Alert."""
        alert = Alert(
            id="repr-test",
            name="Repr Test Alert",
            summary="Testing string representation",
            severity=AlertSeverity.MEDIUM,
        )

        # Test that string contains key information
        alert_str = str(alert)
        assert "Repr Test Alert" in alert_str
        assert "medium" in alert_str or "MEDIUM" in alert_str
