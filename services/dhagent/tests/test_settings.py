"""Tests for the enhanced settings configuration."""

import os
from unittest.mock import Mock, call, patch

from src.config.settings import Settings


class TestSettings:
    """Test cases for the Settings class."""

    def test_settings_without_apollo(self):
        """Test settings when Apollo is not available."""
        error_msg = "Apollo not available"
        with patch(
            "src.config.settings.ApolloConfigReader",
            side_effect=AssertionError(error_msg),
        ):
            settings = Settings()
            assert settings.apollo is None

    def test_settings_with_apollo(self):
        """Test settings when Apollo is available."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = "apollo_value"

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()
            assert settings.apollo is not None

    def test_config_priority_apollo_first(self):
        """Test that Apollo values take priority over environment variables."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = "apollo_value"

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(os.environ, {"APP_NAME": "env_value"}),
        ):
            settings = Settings()
            assert settings.app_name == "apollo_value"
            mock_apollo.get_string.assert_called_with("APP_NAME")  # same key for Apollo

    def test_config_fallback_to_env(self):
        """Test fallback to environment variables when Apollo returns empty."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = ""  # Apollo returns empty

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(os.environ, {"APP_NAME": "env_value"}),
        ):
            settings = Settings()
            assert settings.app_name == "env_value"

    def test_config_fallback_to_default(self):
        """Test fallback to default values when both Apollo and env are empty."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = ""

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(os.environ, {}, clear=True),
        ):
            settings = Settings()
            assert settings.app_name == "dhagent"  # default value

    def test_int_config_type(self):
        """Test integer configuration handling."""
        mock_apollo = Mock()
        mock_apollo.get_int.return_value = 9000

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()
            assert settings.port == 9000
            mock_apollo.get_int.assert_called_with("PORT")  # same key for Apollo

    def test_bool_config_type(self):
        """Test boolean configuration handling."""
        mock_apollo = Mock()
        mock_apollo.get_bool.return_value = True

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()
            assert settings.debug is True
            mock_apollo.get_bool.assert_called_with("DEBUG")  # same key for Apollo

    def test_env_bool_parsing(self):
        """Test environment variable boolean parsing."""
        mock_apollo = Mock()
        # Apollo returns False, should use env
        mock_apollo.get_bool.return_value = False

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(os.environ, {"DEBUG": "true"}),
        ):
            settings = Settings()
            assert settings.debug is True

    def test_list_config_cors_origins(self):
        """Test list configuration for CORS origins."""
        mock_apollo = Mock()
        mock_apollo.get_string_list.return_value = [
            "https://example.com",
            "https://api.example.com",
        ]

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()
            expected = ["https://example.com", "https://api.example.com"]
            assert settings.backend_cors_origins == expected
            # Same key for Apollo
            mock_apollo.get_string_list.assert_called_with("BACKEND_CORS_ORIGINS", is_python_literal=True)

    def test_cors_origins_env_fallback(self):
        """Test CORS origins fallback to environment variable."""
        mock_apollo = Mock()
        mock_apollo.get_string_list.return_value = []  # Apollo returns empty

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(
                os.environ,
                {"BACKEND_CORS_ORIGINS": "http://localhost:3000,http://localhost:8080"},
            ),
        ):
            settings = Settings()
            expected = ["http://localhost:3000", "http://localhost:8080"]
            assert settings.backend_cors_origins == expected

    def test_sensitive_token_config(self):
        """Test sensitive configuration like APIHub token."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = "apollo_secret_token"

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()
            assert settings.apihub_token == "apollo_secret_token"
            # Same key for Apollo
            mock_apollo.get_string.assert_called_with("APIHUB_TOKEN")

    def test_unified_key_pattern(self):
        """Test that the same key is used for both Apollo and environment variables."""
        mock_apollo = Mock()
        mock_apollo.get_string.return_value = "test_value"

        with patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo):
            settings = Settings()

            # Test a few key configurations to ensure they use the same key
            _ = settings.app_name  # Should call Apollo with "APP_NAME"
            _ = settings.apihub_token  # Should call Apollo with "APIHUB_TOKEN"

            # Verify Apollo was called with the exact same keys
            expected_calls = [call("APP_NAME"), call("APIHUB_TOKEN")]
            mock_apollo.get_string.assert_has_calls(expected_calls)

    def test_apollo_exception_handling(self):
        """Test graceful handling of Apollo exceptions during config read."""
        mock_apollo = Mock()
        mock_apollo.get_string.side_effect = Exception("Apollo connection error")

        with (
            patch("src.config.settings.ApolloConfigReader", return_value=mock_apollo),
            patch.dict(os.environ, {"APP_NAME": "fallback_value"}),
        ):
            settings = Settings()
            # Should gracefully fall back to environment variable
            assert settings.app_name == "fallback_value"

