"""Unit tests for ChangeManager."""

from src.usecases.change_management.change_manager import TodoItem, TodoStatus, render_todos_as_markdown


class TestRenderTodosAsMarkdown:
    """Test cases for the render_todos_as_markdown function."""

    def test_render_todos_as_markdown_empty_list(self):
        """Test rendering an empty list of todos."""
        todos = []
        result = render_todos_as_markdown(todos)

        expected = "# Diagnostic Plan Tasks\n\n<Empty Plan>"
        assert result == expected

    def test_render_todos_as_markdown_single_todo_pending(self):
        """Test rendering a single pending todo item."""
        todos = [TodoItem(description="Check application logs for errors", status=TodoStatus.PENDING)]
        result = render_todos_as_markdown(todos)

        expected = "# Diagnostic Plan Tasks\n\nAn array of tasks:\n\n- [ ] Check application logs for errors\n"
        assert result == expected

    def test_render_todos_as_markdown_single_todo_completed(self):
        """Test rendering a single completed todo item."""
        todos = [TodoItem(description="Check application logs for errors", status=TodoStatus.COMPLETED)]
        result = render_todos_as_markdown(todos)

        expected = "# Diagnostic Plan Tasks\n\nAn array of tasks:\n\n- [x] Check application logs for errors\n"
        assert result == expected

    def test_render_todos_as_markdown_multiple_todos(self):
        """Test rendering multiple todo items."""
        todos = [
            TodoItem(description="Check application logs for errors", status=TodoStatus.PENDING),
            TodoItem(description="Analyze CPU and memory metrics", status=TodoStatus.COMPLETED),
            TodoItem(description="Review recent deployment events", status=TodoStatus.PENDING),
        ]
        result = render_todos_as_markdown(todos)

        expected = (
            "# Diagnostic Plan Tasks\n\n"
            "An array of tasks:\n\n"
            "- [ ] Check application logs for errors\n"
            "- [x] Analyze CPU and memory metrics\n"
            "- [ ] Review recent deployment events\n"
        )
        assert result == expected

    def test_render_todos_as_markdown_different_statuses(self):
        """Test rendering todos with different status values."""
        todos = [
            TodoItem(description="First task", status=TodoStatus.PENDING),
            TodoItem(description="Second task", status=TodoStatus.COMPLETED),
            TodoItem(description="Third task", status=TodoStatus.IN_PROGRESS),
            TodoItem(description="Fourth task", status=TodoStatus.FAILED),
        ]
        result = render_todos_as_markdown(todos)

        expected = (
            "# Diagnostic Plan Tasks\n\n"
            "An array of tasks:\n\n"
            "- [ ] First task\n"
            "- [x] Second task\n"
            "- [ ] Third task\n"
            "- [ ] Fourth task\n"
        )
        assert result == expected

    def test_render_todos_as_markdown_only_completed_shows_checkbox(self):
        """Test that only COMPLETED status shows checked checkbox."""
        todos = [
            TodoItem(description="Completed task", status=TodoStatus.COMPLETED),
            TodoItem(description="Pending task", status=TodoStatus.PENDING),
            TodoItem(description="In progress task", status=TodoStatus.IN_PROGRESS),
            TodoItem(description="Failed task", status=TodoStatus.FAILED),
        ]
        result = render_todos_as_markdown(todos)

        expected = (
            "# Diagnostic Plan Tasks\n\n"
            "An array of tasks:\n\n"
            "- [x] Completed task\n"
            "- [ ] Pending task\n"
            "- [ ] In progress task\n"
            "- [ ] Failed task\n"
        )
        assert result == expected

    def test_render_todos_as_markdown_special_characters(self):
        """Test rendering todos with special characters in descriptions."""
        todos = [
            TodoItem(description="Check logs for 'error' patterns & exceptions", status=TodoStatus.PENDING),
            TodoItem(description="Analyze metrics: CPU > 80% OR memory > 90%", status=TodoStatus.COMPLETED),
        ]
        result = render_todos_as_markdown(todos)

        expected = (
            "# Diagnostic Plan Tasks\n\n"
            "An array of tasks:\n\n"
            "- [ ] Check logs for 'error' patterns & exceptions\n"
            "- [x] Analyze metrics: CPU > 80% OR memory > 90%\n"
        )
        assert result == expected

    def test_render_todos_as_markdown_long_descriptions(self):
        """Test rendering todos with long descriptions."""
        long_description = (
            "This is a very long task description that spans multiple lines "
            "and contains detailed information about what needs to be done "
            "during the diagnostic process including checking various metrics"
        )
        todos = [TodoItem(description=long_description, status=TodoStatus.PENDING)]
        result = render_todos_as_markdown(todos)

        expected = f"# Diagnostic Plan Tasks\n\nAn array of tasks:\n\n- [ ] {long_description}\n"
        assert result == expected


class TestTodoItem:
    """Test cases for the TodoItem model."""

    def test_todo_item_creation_with_defaults(self):
        """Test creating a TodoItem with default status."""
        todo = TodoItem(description="Test task")

        assert todo.description == "Test task"
        assert todo.status == TodoStatus.PENDING

    def test_todo_item_creation_with_custom_status(self):
        """Test creating a TodoItem with custom status."""
        todo = TodoItem(description="Completed task", status=TodoStatus.COMPLETED)

        assert todo.description == "Completed task"
        assert todo.status == TodoStatus.COMPLETED

    def test_todo_item_with_empty_description(self):
        """Test creating TodoItem with empty description."""
        todo = TodoItem(description="")

        assert todo.description == ""
        assert todo.status == TodoStatus.PENDING

    def test_todo_item_string_representation(self):
        """Test string representation of TodoItem."""
        todo = TodoItem(description="Test task", status=TodoStatus.IN_PROGRESS)

        todo_str = str(todo)
        assert "Test task" in todo_str
        assert "in_progress" in todo_str or "IN_PROGRESS" in todo_str


class TestTodoStatus:
    """Test cases for the TodoStatus enum."""

    def test_todo_status_values(self):
        """Test TodoStatus enum values."""
        assert TodoStatus.PENDING.value == "pending"
        assert TodoStatus.IN_PROGRESS.value == "in_progress"
        assert TodoStatus.COMPLETED.value == "completed"
        assert TodoStatus.FAILED.value == "failed"

    def test_todo_status_enum_members(self):
        """Test TodoStatus enum has all expected members."""
        expected_statuses = {"PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"}
        actual_statuses = {status.name for status in TodoStatus}
        assert actual_statuses == expected_statuses
