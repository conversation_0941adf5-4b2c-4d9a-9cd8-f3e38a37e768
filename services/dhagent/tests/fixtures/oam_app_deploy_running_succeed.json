{"data": {"app": {"appDesc": "可观测成本中心", "appName": "observability-cost", "appNameCn": "observability-cost", "appStatus": 1, "appType": "cloud-native", "createTime": "2024-06-18 09:26:14", "creationSource": "new", "creator": "zhouzheng5", "difyWorkspaceId": null, "extensions": null, "fromAppId": null, "id": 4481, "owner": "zhouzheng5", "tenantId": "vwofxn", "updateTime": "2024-06-18 09:26:14"}, "components": [{"appId": 4481, "artifact": {"appId": 4481, "artifact": "observability-cost-ar0618", "branch": "master", "codeQualityScanResult": null, "codeSecurityLevel": null, "commitId": "d6cd359b", "commitMsg": "修复没有数据的bug", "commitTime": null, "committer": null, "componentId": 7422, "createTime": "2025-05-23 11:16:08", "creator": "zhouzheng5", "fromWorkflowName": "licloud-inst-vwofxn-observability-cost-pl0618-0523-j9hj4", "id": 822382, "image": "artifactory.chehejia.com/licloud-docker-dev-local/vwofxn/cicd/observability-cost-ar0618:1.0.0-d6cd359b-250523111529", "imageCreateTime": "2025-05-23 11:15:40", "imageCreator": "zhouzheng5", "imageSecurityScanJobResult": null, "imageTag": "1.0.0-d6cd359b-250523111529", "isChosen": false, "isDeleted": false, "pipelineId": 815379, "prodImage": "artifactory.chehejia.com/licloud-docker-dev-local/vwofxn/cicd/observability-cost-ar0618:1.0.0-d6cd359b-250523111529", "updateTime": "2025-05-23 11:16:12", "version": "1.0.0"}, "artifactKey": "observability-cost-ar0618", "classify": null, "componentDesc": "", "componentLevel": 1, "componentName": "observability-cost", "componentNameCn": "observability-cost", "componentStatus": 1, "componentType": "stateless-service", "componentTypeName": null, "createTime": "2024-06-18 09:26:14", "creator": "zhouzheng5", "deployType": null, "id": 7422, "language": "golang", "updateTime": "2024-06-18 09:26:14", "vehicleCost": 0}], "history": {"appId": 4481, "appVersion": "1.0.0", "batchId": 0, "componentId": 7422, "createTime": "2025-08-12 18:22:15", "data": {"deployParam": [{"componentData": {"cmd": "", "cpu": "1", "memory": "2Gi", "port": "8080", "processParam": "", "programParam": "", "replicas": 1}, "componentName": "observability-cost", "traits": [{"traitData": {"enable": true, "env": [{"name": "env", "value": "dev"}]}, "traitName": "env", "version": "v1.0"}, {"traitData": {"collectMode": "multi-line", "enable": true, "filePattern": ".log", "logBeginRegex": "([\\[]v1|\\[\\d{4}-\\d{1,2}-\\d{1,2}\\]|[\\[]20).", "logPath": "/chj/data/log/observability-cost"}, "traitName": "log", "version": "v1.7"}, {"traitData": {"configMap": [{"data": [{"key": "application-dev.yaml", "value": "mock_value"}], "mountOnly": false, "mountPath": "/conf", "name": "licfg-observability-cost"}], "enable": true}, "traitName": "configmap", "version": "v1.4"}], "version": "v2.5"}]}, "deployMsg": null, "deployStatus": 1, "domain": "devops", "duration": 4273, "env": "dev", "execType": "manual", "id": 1126156, "isAutoNext": true, "isTransfer": 0, "jobId": 21116, "jobName": "devops-dev-cnhbnp01", "jobType": "deploy", "operator": "zhouzheng5", "pipelineId": 815379, "referenceCommitId": "2724163", "referenceName": "2724163", "runtimeStatus": 11, "stageName": "DEV", "template": {"stages": [{"jobs": [[{"branch": null, "createTime": "2024-06-18T09:26:14", "creator": "zhouzheng5", "data": null, "domain": "devops", "env": "dev", "id": 21116, "isAuto": false, "isDeleted": false, "isPreSuccess": false, "jobLabel": null, "jobName": "devops-dev-cnhbnp01", "jobType": "deploy", "preJobId": null, "relyJobId": null, "stageName": "DEV", "templateId": 4562, "updateTime": "2024-06-18T09:26:14", "vdc": "cnhbnp01", "vdcDesc": "默认集群，一般情况下优先使用该集群，运行在百度云-华北-北京区域", "vdcName": "中国华北01(默认)"}]], "stageName": "DEV"}, {"jobs": [], "stageName": "TEST"}, {"jobs": [], "stageName": "ONTEST"}, {"jobs": [[{"branch": "master", "createTime": "2024-06-18 09:26:14", "creator": "zhouzheng5", "data": null, "domain": "devops", "env": "prod", "id": 21117, "isAuto": false, "isDeleted": false, "isPreSuccess": false, "jobLabel": null, "jobName": "devops-prod-cn01", "jobType": "deploy", "preJobId": null, "relyJobId": null, "stageName": "PROD", "templateId": 4562, "updateTime": "2024-06-18 09:43:12", "vdc": "cn01"}]], "stageName": "PROD"}], "templateId": 4562}, "updateTime": "2025-08-12 18:22:22", "vdc": "cnhbnp01", "yaml": "xxx"}, "reason": null}, "operator": "zhouzheng5"}