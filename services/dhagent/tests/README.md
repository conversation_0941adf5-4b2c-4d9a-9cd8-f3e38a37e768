# Tests

This directory contains unit and integration tests for the dhagent.

## Running Tests

### Install test dependencies

```bash
# Install test dependencies using uv
uv add --group test pytest pytest-asyncio pytest-cov httpx

# Or install all test dependencies at once
uv sync --group test
```

### Run all tests

```bash
# Run tests using uv
uv run pytest

# Run with verbose output
uv run pytest -v
```

### Run specific test categories

```bash
# Run unit tests only
uv run pytest tests/test_alert.py tests/test_alert_processor.py

# Run API integration tests
uv run pytest tests/test_api.py

# Run with coverage
uv run pytest --cov=src --cov-report=term-missing

# Generate HTML coverage report
uv run pytest --cov=src --cov-report=html
```

## Test Categories

- **Unit Tests**: Test individual components in isolation
  - Alert entity validation and behavior
  - Alert processor workflow and processing logic
  - Domain exception classes and inheritance

- **Integration Tests**: Test component interactions
  - API endpoint functionality
  - End-to-end alert processing
  - Request/response validation

## Fixtures

The `conftest.py` file provides shared fixtures:

- `client`: FastAPI test client
- `sample_alert`: Pre-configured Alert entity for testing
- `sample_alert_data`: Sample alert data for API requests
- `event_loop`: Async event loop for async tests

## Example Test Run

```bash
$ uv run pytest -v
======================== test session starts ========================
tests/test_alert.py::TestAlert::test_alert_creation PASSED
tests/test_alert.py::TestAlert::test_alert_with_optional_fields PASSED
tests/test_alert_processor.py::TestAlertProcessor::test_processor_initialization PASSED
tests/test_alert_processor.py::TestAlertProcessor::test_process_hello_with_alert PASSED
======================== 19 passed in 0.10s ========================
```
