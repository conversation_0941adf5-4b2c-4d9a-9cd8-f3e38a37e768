"""
Tests for the topology tool.
"""

import json

import pytest
from langchain_core.tools import StructuredTool

from src.usecases.platform.tools.topology import (
    Edge,
    Node,
    NodeStatus,
    ServiceTopology,
    _apply_error_overlay,
    _get_connected_services,
    get_service_topology,
)

# Create topology tool for testing
topology_tool = StructuredTool.from_function(
    func=get_service_topology,
    name="get_service_topology",
    coroutine=get_service_topology,
)


class TestTopologyModels:
    """Test the data models."""

    def test_node_creation(self):
        """Test Node model creation."""
        node = Node(
            service_name="test-service",
            node_type="service",
            status=NodeStatus.HEALTHY,
            alert_count=5,
            error_log_count=10,
            error_trace_count=2,
        )

        assert node.service_name == "test-service"
        assert node.node_type == "service"
        assert node.status == NodeStatus.HEALTHY
        assert node.alert_count == 5
        assert node.error_log_count == 10
        assert node.error_trace_count == 2

    def test_edge_creation(self):
        """Test Edge model creation."""
        edge = Edge(source="service-a", target="service-b", relationship_type="calls")

        assert edge.source == "service-a"
        assert edge.target == "service-b"
        assert edge.relationship_type == "calls"

    def test_service_topology_creation(self):
        """Test ServiceTopology model creation."""
        node = Node(service_name="test-service", node_type="service")
        edge = Edge(source="test-service", target="other-service", relationship_type="calls")

        topology = ServiceTopology(nodes=[node], edges=[edge])

        assert len(topology.nodes) == 1
        assert len(topology.edges) == 1


class TestTopologyTool:
    """Test the topology tool function."""

    @pytest.mark.asyncio
    async def test_get_topology_success(self):
        """Test successful topology retrieval."""
        result = await get_service_topology("user-service")

        assert isinstance(result, str)
        topology_data = json.loads(result)

        assert "nodes" in topology_data
        assert "edges" in topology_data
        assert len(topology_data["nodes"]) >= 1  # Should have at least user-service

        # Check that the user-service is included
        node_names = [node["service_name"] for node in topology_data["nodes"]]
        assert "user-service" in node_names

    @pytest.mark.asyncio
    async def test_get_topology_with_error_overlay(self):
        """Test topology retrieval with error overlay."""
        result = await get_service_topology("payment-service", include_error_overlay=True)

        assert isinstance(result, str)
        topology_data = json.loads(result)

        # Check that status was updated based on error counts
        payment_node = next(node for node in topology_data["nodes"] if node["service_name"] == "payment-service")
        assert payment_node["status"] == "critical"  # Should be critical due to high error count

    @pytest.mark.asyncio
    async def test_get_topology_full(self):
        """Test topology retrieval for full topology."""
        result = await get_service_topology()

        topology_data = json.loads(result)
        assert len(topology_data["nodes"]) == 5  # All services
        assert topology_data["metadata"]["service_filter"] is None

    @pytest.mark.asyncio
    async def test_get_topology_with_service_filter(self):
        """Test topology retrieval with service filter."""
        result = await get_service_topology("order-service", depth=1)

        topology_data = json.loads(result)
        assert topology_data["metadata"]["service_filter"] == "order-service"

        # Should include order-service and its direct connections
        node_names = [node["service_name"] for node in topology_data["nodes"]]
        assert "order-service" in node_names

    @pytest.mark.asyncio
    async def test_get_topology_depth_filtering(self):
        """Test topology retrieval with depth filtering."""
        result = await get_service_topology("payment-service", depth=1)

        topology_data = json.loads(result)
        assert "payment-service" in [node["service_name"] for node in topology_data["nodes"]]

    @pytest.mark.asyncio
    async def test_get_topology_order_service(self):
        """Test topology retrieval for order-service."""
        result = await get_service_topology("order-service", include_error_overlay=True)

        topology_data = json.loads(result)
        assert topology_data["metadata"]["service_filter"] == "order-service"

        # Check that payment-service has critical status if included
        payment_nodes = [node for node in topology_data["nodes"] if node["service_name"] == "payment-service"]
        if payment_nodes:
            assert payment_nodes[0]["status"] == "critical"  # Should be critical due to high error count


class TestHelperFunctions:
    """Test the helper functions."""

    def test_apply_error_overlay(self):
        """Test error overlay application."""
        nodes = [
            Node(service_name="healthy", node_type="service", alert_count=0, error_log_count=0, error_trace_count=0),
            Node(service_name="warning", node_type="service", alert_count=1, error_log_count=10, error_trace_count=2),
            Node(service_name="critical", node_type="service", alert_count=5, error_log_count=20, error_trace_count=10),
        ]

        result = _apply_error_overlay(nodes)

        assert result[0].status == NodeStatus.HEALTHY
        assert result[1].status == NodeStatus.WARNING
        assert result[2].status == NodeStatus.CRITICAL

    def test_get_connected_services(self):
        """Test getting connected services."""
        edges = [
            Edge(source="service-a", target="service-b", relationship_type="calls"),
            Edge(source="service-b", target="service-c", relationship_type="calls"),
            Edge(source="service-c", target="service-d", relationship_type="calls"),
        ]

        # Test depth 1
        connected = _get_connected_services("service-a", edges, 1)
        assert "service-b" in connected
        assert "service-c" not in connected

        # Test depth 2
        connected = _get_connected_services("service-a", edges, 2)
        assert "service-b" in connected
        assert "service-c" in connected
        assert "service-d" not in connected

        # Test depth 3
        connected = _get_connected_services("service-a", edges, 3)
        assert "service-b" in connected
        assert "service-c" in connected
        assert "service-d" in connected


class TestLangchainIntegration:
    """Test the langchain tool integration."""

    def test_topology_tool_instance(self):
        """Test the topology tool instance."""
        assert topology_tool.name == "get_service_topology"
        assert hasattr(topology_tool, "description")
        assert hasattr(topology_tool, "args_schema")

    @pytest.mark.asyncio
    async def test_langchain_tool_execution(self):
        """Test executing the langchain tool."""
        result = await topology_tool.ainvoke(
            {"service_name": "user-service", "depth": 2, "include_error_overlay": True, "dependency_type": "all"}
        )

        assert isinstance(result, str)
        assert "user-service" in result
        assert "nodes" in result
        assert "edges" in result
