#!/usr/bin/env python3
"""
Test script to verify the fixed RobotHttpClient with IDaaS client injection.
"""

import asyncio
from unittest.mock import AsyncMock, patch

from src.entities.robot import CreateConversationRequest
from src.external.idaas.client import IDaaSClient
from src.external.robot.http_client import RobotHttpClient


async def test_robot_client_with_idaas_injection():
    """Test RobotHttpClient with injected IDaaS client."""
    print("🔍 Testing RobotHttpClient with IDaaS client injection...")
    
    # Create and configure IDaaS client
    idaas_client = IDaaSClient()
    idaas_client.initialize(
        client_id="test_client_id",
        client_secret="test_client_secret",
        service_id="test_service_id",
        endpoint="https://test-idaas.com/api",
        scopes=["robot:conversation"]
    )
    
    # Create HTTP client with IDaaS client
    robot_client = RobotHttpClient(
        base_url="https://test-robot-api.com",
        idaas_client=idaas_client,
        timeout=30.0
    )
    
    print("✅ RobotHttpClient created successfully with IDaaS client")
    
    # Mock the TokenManager to avoid real network calls
    with patch.object(idaas_client, '_token_manager') as mock_token_manager:
        mock_bundle = AsyncMock()
        mock_bundle.access_token = "test_m2m_token_12345"
        mock_token_manager.get_token.return_value = mock_bundle
        
        # Mock HTTP client
        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": {
                    "id": 5235,
                    "title": "Test Conversation",
                    "chat_id": "oc_123456",
                    "user_ldap_name": "testuser",
                    "primary_assistant_name": "assistant",
                    "primary_assistant_display_name": "Test Assistant",
                    "metadata": {"feedback": None, "users": []},
                    "create_time": 1753955523.049272,
                    "update_time": 1753955523.049297,
                }
            }
            mock_client.request.return_value = mock_response
            
            # Create test request
            request = CreateConversationRequest(
                title="Test Conversation",
                scenario="deepheal",
                metadata={"members": {"id_type": "ldap_name", "ids": ["user1"]}}
            )
            
            # Execute request
            result = await robot_client.create_conversation(request)
            
            # Verify token manager was called
            print("✅ Token manager called:", mock_token_manager.get_token.called)
            
            # Verify HTTP request was made
            print("✅ HTTP request made:", mock_client.request.called)
            
            # Check the actual headers sent
            call_args = mock_client.request.call_args
            headers = call_args[1]["headers"]
            
            print(f"📋 Headers sent: {headers}")
            
            # Verify Authorization header
            assert "Authorization" in headers
            assert headers["Authorization"] == "Bearer test_m2m_token_12345"
            print("✅ Authorization header correct:", headers["Authorization"])
            
            # Verify response
            assert result.code == 0
            assert result.data.id == 5235
            print("✅ Response parsed correctly")
            
            print("\n🎉 All checks passed! IDaaS client injection working correctly.")


async def test_factory_creates_client_correctly():
    """Test that the factory creates the client with proper IDaaS injection."""
    print("\n🔍 Testing factory creates client with IDaaS injection...")
    
    from src.external.robot.factory import RobotServiceFactory
    
    # Mock the IDaaS initialization to avoid real network calls
    with patch("src.external.robot.factory.IDaaSClient") as mock_idaas_class:
        mock_idaas_instance = AsyncMock()
        mock_idaas_class.return_value = mock_idaas_instance
        
        # Create robot service through factory
        robot_service = RobotServiceFactory.create_robot_service(
            base_url="https://test-api.com",
            timeout=60.0
        )
        
        print("✅ Robot service created through factory")
        
        # Verify IDaaS client was created and initialized
        mock_idaas_class.assert_called_once()
        mock_idaas_instance.initialize.assert_called_once()
        
        # Check initialization parameters
        init_call = mock_idaas_instance.initialize.call_args
        init_kwargs = init_call[1] if init_call[1] else {}
        init_args = init_call[0] if init_call[0] else []
        
        print("✅ IDaaS client initialized with proper parameters")
        print(f"📋 Initialization called with args: {init_args}")
        print(f"📋 Initialization called with kwargs: {init_kwargs}")
        
        print("\n🎉 Factory test passed! IDaaS client properly injected.")


async def main():
    """Run all tests."""
    print("🚀 Testing Fixed RobotHttpClient with IDaaS Injection")
    print("=" * 60)
    
    try:
        await test_robot_client_with_idaas_injection()
        await test_factory_creates_client_correctly()
        
        print("\n" + "=" * 60)
        print("🎯 Summary:")
        print("• RobotHttpClient now properly holds IDaaS client instance")
        print("• M2M token is correctly retrieved and injected")
        print("• Factory creates and configures IDaaS client properly")
        print("• No more global state dependencies")
        print("• Better testability with dependency injection")
        print("• Fixed the 'missing self argument' error")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
