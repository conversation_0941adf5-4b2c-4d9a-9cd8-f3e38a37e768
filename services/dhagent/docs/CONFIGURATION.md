# Configuration Management

This document describes the enhanced configuration management system that provides elegant fallback between Apollo Configuration, environment variables, and default values.

## Overview

The `Settings` class now implements a three-tier configuration priority system:

1. **Apollo Configuration** (highest priority)
2. **Environment Variables** (fallback)
3. **Default Values** (fallback of last resort)

## Key Features

### Unified Key Pattern
- Uses the same key for both Apollo configuration and environment variables
- Example: `APP_NAME` is used as-is for both Apollo and environment variable lookup
- Maximum simplicity: one key, multiple sources

### Graceful Degradation
- If Apollo is not available (missing credentials, network issues), the system automatically falls back to environment variables
- No application crashes due to configuration issues

### Type Safety
- Proper type conversion for `int`, `bool`, `float`, and `string` values
- Validation and error handling for type conversions

### Consistent API
- All configuration values are accessed as properties: `settings.app_name`, `settings.port`, etc.
- Same interface regardless of configuration source

## Configuration Mapping

The same key is used for both Apollo configuration and environment variables:

| Property | Configuration Key | Default |
|----------|------------------|---------|
| `app_name` | `APP_NAME` | `"dhagent"` |
| `app_version` | `APP_VERSION` | `"0.1.0"` |
| `app_description` | `APP_DESCRIPTION` | `"A FastAPI application for AIOps"` |
| `host` | `HOST` | `"0.0.0.0"` |
| `port` | `PORT` | `8000` |
| `debug` | `DEBUG` | `False` |
| `api_v1_prefix` | `API_V1_PREFIX` | `"/api/v1"` |
| `backend_cors_origins` | `BACKEND_CORS_ORIGINS` | `["*"]` |
| `log_level` | `LOG_LEVEL` | `"INFO"` |
| `apihub_token` | `APIHUB_TOKEN` | `""` |

## Usage Examples

### Basic Usage
```python
from src.config.settings import settings

# Access configuration values
app_name = settings.app_name
port = settings.port
debug_mode = settings.debug
```

### Apollo Configuration Format

Apollo uses the same keys as environment variables:

```
# Simple values
APP_NAME=MyApplication
PORT=8080
DEBUG=true
APIHUB_TOKEN=secret_token_value
API_V1_PREFIX=/api/v2
LOG_LEVEL=DEBUG
```

For list values (CORS origins):
```
BACKEND_CORS_ORIGINS=["https://example.com", "https://api.example.com", "https://admin.example.com"]
```

### Environment Variables

Set environment variables as usual:
```bash
export APP_NAME="MyApplication"
export PORT="8080"
export DEBUG="true"
export APIHUB_TOKEN="secret_token_value"
export BACKEND_CORS_ORIGINS="https://example.com,https://api.example.com"
```

## Implementation Details

### The `_get_config` Method

The core of the system is the simplified `_get_config` method:

```python
def _get_config(self, key: str, default: Any = "", config_type: str = "string") -> Any:
    # 1. Try Apollo first (using the same key)
    if self.apollo:
        value = self.apollo.get_string(key)  # or get_int, get_bool, etc.
        if value:
            return value

    # 2. Fallback to environment variable (using the same key)
    env_value = os.getenv(key)
    if env_value is not None:
        return convert_type(env_value, config_type)

    # 3. Return default
    return default
```

### Special Handling for Lists

List configurations (like CORS origins) have special handling:
- Apollo: Uses `get_string_list()` with Python literal evaluation
- Environment: Comma-separated values that get split and trimmed

### Error Handling

- Apollo connection failures are caught and logged, falling back gracefully
- Type conversion errors use default values
- Invalid Apollo responses don't crash the application

## Benefits

1. **Flexibility**: Can be configured via Apollo, environment variables, or both
2. **Reliability**: Graceful degradation when Apollo is unavailable
3. **Security**: Sensitive values (tokens, secrets) can be managed in Apollo
4. **Development-friendly**: Easy to override with environment variables during development
5. **Production-ready**: Centralized configuration management via Apollo in production

## Migration Guide

If you're migrating from the old configuration system:

### Before
```python
class Settings:
    app_name: str = "dhagent"
    port: int = int(os.getenv("PORT", "8000"))
    apihub_token: str = os.getenv("APIHUB_TOKEN", "")
```

### After
```python
@property
def app_name(self) -> str:
    return self._get_config("APP_NAME", "dhagent")

@property
def port(self) -> int:
    return self._get_config("PORT", 8000, "int")

@property
def apihub_token(self) -> str:
    return self._get_config("APIHUB_TOKEN", "")
```

The usage remains the same: `settings.app_name`, `settings.port`, `settings.apihub_token`

**Key improvement**: Now uses the same key (`APP_NAME`, `PORT`, `APIHUB_TOKEN`) for both Apollo and environment variables - maximum simplicity!
