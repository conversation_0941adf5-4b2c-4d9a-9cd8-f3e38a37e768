# Linting and Code Quality

This document outlines the linting and code quality tools configured for the SRE Copilot project.

## Tools Overview

### 🎨 **Code Formatters**
- **Black**: Uncompromising Python code formatter
- **isort**: Python import sorter
- **Ruff**: Fast Python linter and formatter

### 🔍 **Linters**
- **Ruff**: Fast Python linter (replaces flake8, pylint, and more) ✅ **All checks passing**
- **MyPy**: Static type checker for Python ✅ **All checks passing**

### 🪝 **Pre-commit Hooks**
- **pre-commit**: Git hooks framework for code quality enforcement

## Installation

```bash
# Install development dependencies (includes all linting tools)
uv sync --group dev --group test

# Install pre-commit hooks
make pre-commit-install
# or
uv run pre-commit install
```

## Usage

### Quick Development Workflow

```bash
# Complete development setup
make dev-setup

# Quick check during development
make quick-check

# CI-style full check
make ci-check
```

### Individual Tools

```bash
# Format code
make format                    # Format with black, isort, and ruff
uv run black src tests        # Format with black only
uv run isort src tests        # Sort imports only
uv run ruff format src tests  # Format with ruff only

# Lint code
make lint                     # Run all linters
uv run ruff check src tests  # Check with ruff
uv run mypy src              # Type check with mypy

# Auto-fix issues
make check                    # Auto-fix with ruff
uv run ruff check --fix src tests

# Run tests
make test                     # Basic test run
make test-cov                 # With coverage report
```

### Pre-commit Hooks

```bash
# Run pre-commit on all files
make pre-commit-run

# Run pre-commit on staged files (automatic on git commit)
git commit -m "Your message"
```

## Configuration Details

### Black Configuration
- **Line length**: 88 characters
- **Target version**: Python 3.12
- **Profile**: Compatible with isort

### isort Configuration
- **Profile**: black (for compatibility)
- **Line length**: 88 characters
- **Known first party**: src

### Ruff Configuration
- **Line length**: 88 characters
- **Target version**: Python 3.12
- **Selected rules**:
  - E, W (pycodestyle)
  - F (pyflakes)
  - I (isort)
  - B (flake8-bugbear)
  - C4 (flake8-comprehensions)
  - UP (pyupgrade)
  - PL (pylint)
  - PT (flake8-pytest-style)

### MyPy Configuration
- **Python version**: 3.12
- **Strict mode**: Enabled for src/
- **Relaxed mode**: For tests/
- **Ignore missing imports**: For third-party packages (langchain, etc.)

## Team Guidelines

### Code Style Standards
1. **Line length**: Maximum 88 characters
2. **Imports**: Sorted alphabetically, grouped by type
3. **Type hints**: Required for all public functions
4. **Docstrings**: Required for all public classes and functions
5. **Testing**: Minimum 80% code coverage

### Pre-commit Requirements
All commits must pass:
- ✅ Black formatting
- ✅ isort import sorting
- ✅ Ruff linting (with auto-fixes)
- ✅ MyPy type checking
- ✅ Basic file checks (trailing whitespace, etc.)
- ✅ Test suite

**Current Status**: 🎉 **All linting tools are passing with zero errors!**

### IDE Integration

#### VS Code
Add to `.vscode/settings.json`:
```json
{
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.mypyEnabled": true,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### PyCharm
1. Install plugins: Black, Ruff
2. Configure external tools for formatters
3. Enable MyPy plugin
4. Set up file watchers for auto-formatting

## Troubleshooting

### Common Issues

**Import sorting conflicts**:
```bash
# Fix with isort
uv run isort --profile black src tests
```

**Type checking errors**:
```bash
# Check specific file
uv run mypy src/path/to/file.py

# Ignore missing imports for development
# Add: # type: ignore
```

**Pre-commit hook failures**:
```bash
# Run individual hooks
uv run pre-commit run black --all-files
uv run pre-commit run ruff --all-files

# Update hook versions
uv run pre-commit autoupdate
```

### Bypassing Checks (Emergency Only)
```bash
# Skip pre-commit hooks (NOT RECOMMENDED)
git commit --no-verify -m "Emergency fix"

# Skip specific ruff rules
# Add: # noqa: E501 (for line too long)
```

## Continuous Integration

The following checks run on every commit:
1. Code formatting verification
2. Import sorting verification
3. Linting with ruff
4. Type checking with mypy
5. Test suite execution
6. Coverage reporting

## Performance

- **Ruff**: ~10-100x faster than traditional tools
- **Black**: Consistent 1-2 second formatting
- **MyPy**: Incremental checking for speed
- **Pre-commit**: Cached for subsequent runs

Run `make clean` periodically to clear caches if needed.
