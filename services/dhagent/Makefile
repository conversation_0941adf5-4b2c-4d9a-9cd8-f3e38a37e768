# Makefile for development

.PHONY: help install install-dev lint format check test clean pre-commit-install

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## Install production dependencies
	uv sync

install-dev: ## Install development dependencies
	uv sync --group dev --group test

update-deps: ## Update dependencies and regenerate lock file
	uv lock --upgrade
	uv sync --group dev --group test

lint: ## Run all linting tools
	uv run ruff check src tests
	uv run mypy src --ignore-missing-imports

lint-fix: ## Run all linting tools with auto-fix
	uv run ruff check --fix src tests
	uv run ruff format src tests
	uv run mypy src --ignore-missing-imports

format: ## Format code with ruff
	uv run ruff format src tests
	uv run ruff check --fix src tests

check: ## Run ruff with auto-fix
	uv run ruff check --fix src tests

test: ## Run tests
	uv run pytest -v

test-cov: ## Run tests with coverage
	uv run pytest --cov=src --cov-report=term-missing --cov-report=html

clean: ## Clean up cache files
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .mypy_cache
	rm -rf .ruff_cache
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf dist
	rm -rf build

pre-commit-install: ## Install pre-commit hooks
	uv run pre-commit install

pre-commit-run: ## Run pre-commit on all files
	uv run pre-commit run --all-files

# Development workflow commands
dev-setup: install-dev pre-commit-install ## Complete development setup

ci-check: lint test ## Run CI checks (lint + test)

quick-check: format check test ## Quick development check

# Server commands
serve: ## Start the development server
	uv run uvicorn src.interfaces.api.app:app --reload --host 0.0.0.0 --port 8000

serve-prod: ## Start the production server
	uv run gunicorn src.interfaces.api.app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
