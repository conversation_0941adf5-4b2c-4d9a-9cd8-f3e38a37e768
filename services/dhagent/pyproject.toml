[project]
name = "dhagent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "apollo-client-python>=1.0.1",
    "cloudevents>=1.12.0",
    "fastapi>=0.116.1",
    "gunicorn>=23.0.0",
    "langchain>=0.3.26",
    "langchain-openai>=0.3.28",
    "langfuse>=3.2.6",
    "langgraph>=0.5.3",
    "li-agentcore",
    "pydantic>=2.11.7",
    "pytest>=8.4.1",
    "python-dotenv>=1.1.1",
    "uvicorn>=0.35.0",
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.25.0",
    "pytest-cov>=5.0.0",
    "httpx>=0.27.0",
]

dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.25.0",
    "pytest-cov>=5.0.0",
    "httpx>=0.27.0",
    "flake8>=7.0.0",
]

[dependency-groups]
dev = [
    "mypy>=1.17.0",
    "pre-commit>=4.2.0",
    "ruff>=0.12.4",
]
test = [
    "httpx>=0.28.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
]

# Ruff configuration
[tool.ruff]
line-length = 120
target-version = "py312"
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TC",  # flake8-type-checking
    "TID", # flake8-tidy-imports
    "ICN", # flake8-import-conventions
    "PL",  # pylint
    "PT",  # flake8-pytest-style
]
ignore = [
    "PLR0913", # Too many arguments to function call
    "PLR0915", # Too many statements
    "PLR2004", # Magic value used in comparison
    "SIM108",  # if-else-block can be replaced with ternary operator
    "ARG002",  # Unused argument
    "ARG001",  # Unused argument in function
    "W293",    # Blank line contains whitespace
]

[tool.ruff.lint.per-file-ignores]
"tests/**/*" = ["PLR2004", "S101", "TID252"]

[tool.ruff.lint.isort]
known-first-party = ["src"]
combine-as-imports = true
force-wrap-aliases = true
split-on-trailing-comma = true

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# MyPy configuration
[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
extra_checks = true
disable_error_code = ["type-arg"]

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false


[[tool.mypy.overrides]]
module = [
    "langchain.*",
    "langgraph.*",
    "langsmith.*",
]
ignore_missing_imports = true

[tool.uv.sources]
apollo-client-python = { git = "https://github.com/gamersover/apollo-client-python.git" }
li-agentcore = { path = "../../packages/li_agentcore", editable = true }

[[tool.uv.index]]
url = "https://artifactory.ep.chehejia.com/artifactory/api/pypi/licloud-pypi/simple"
