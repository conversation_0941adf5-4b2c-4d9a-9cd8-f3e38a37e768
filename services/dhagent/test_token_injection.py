#!/usr/bin/env python3
"""
Test script to verify M2M token injection in Robot HTTP client.
"""

import asyncio
from unittest.mock import AsyncMock, patch

from src.entities.robot import CreateConversationRequest
from src.external.robot.http_client import RobotHttpClient


async def test_token_injection():
    """Test that M2M token is properly injected into headers."""
    print("🔍 Testing M2M token injection in create_conversation...")
    
    # Create client
    client = RobotHttpClient("https://test-api.com")
    
    # Mock IDaaS client
    with patch("src.external.robot.http_client.idaas_client") as mock_idaas:
        mock_idaas.get_token_async.return_value = "test_m2m_token_12345"
        
        # Mock HTTP client
        with patch("httpx.AsyncClient") as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "code": 0,
                "msg": "success",
                "data": {
                    "id": 5235,
                    "title": "Test Conversation",
                    "chat_id": "oc_123456",
                    "user_ldap_name": "testuser",
                    "primary_assistant_name": "assistant",
                    "primary_assistant_display_name": "Test Assistant",
                    "metadata": {"feedback": None, "users": []},
                    "create_time": 1753955523.049272,
                    "update_time": 1753955523.049297,
                }
            }
            mock_client.request.return_value = mock_response
            
            # Create test request
            request = CreateConversationRequest(
                title="Test Conversation",
                scenario="deepheal",
                metadata={"members": {"id_type": "ldap_name", "ids": ["user1"]}}
            )
            
            # Execute request
            result = await client.create_conversation(request)
            
            # Verify IDaaS token was requested
            print("✅ IDaaS client called:", mock_idaas.get_token_async.called)
            mock_idaas.get_token_async.assert_called_once()
            
            # Verify HTTP request was made
            print("✅ HTTP request made:", mock_client.request.called)
            mock_client.request.assert_called_once()
            
            # Check the actual headers sent
            call_args = mock_client.request.call_args
            headers = call_args[1]["headers"]
            
            print(f"📋 Headers sent: {headers}")
            
            # Verify Authorization header
            assert "Authorization" in headers
            assert headers["Authorization"] == "Bearer test_m2m_token_12345"
            print("✅ Authorization header correct:", headers["Authorization"])
            
            # Verify request details
            assert call_args[1]["method"] == "POST"
            assert call_args[1]["url"] == "https://test-api.com/api/v2/conversations"
            print("✅ Request method and URL correct")
            
            # Verify request body
            json_data = call_args[1]["json"]
            assert json_data["title"] == "Test Conversation"
            assert json_data["scenario"] == "deepheal"
            print("✅ Request body correct")
            
            # Verify response
            assert result.code == 0
            assert result.data.id == 5235
            print("✅ Response parsed correctly")
            
            print("\n🎉 All checks passed! M2M token is properly injected.")


async def test_auth_headers_directly():
    """Test the _get_auth_headers method directly."""
    print("\n🔍 Testing _get_auth_headers method directly...")
    
    client = RobotHttpClient("https://test-api.com")
    
    with patch("src.external.robot.http_client.idaas_client") as mock_idaas:
        mock_idaas.get_token_async.return_value = "direct_test_token"
        
        # Test without SAID token
        headers = await client._get_auth_headers()
        print(f"📋 Headers without SAID token: {headers}")
        assert headers == {"Authorization": "Bearer direct_test_token"}
        print("✅ Headers without SAID token correct")
        
        # Test with SAID token
        headers_with_said = await client._get_auth_headers("said_token_123")
        expected_headers = {
            "Authorization": "Bearer direct_test_token",
            "Client-Assertion": "Bearer said_token_123"
        }
        print(f"📋 Headers with SAID token: {headers_with_said}")
        assert headers_with_said == expected_headers
        print("✅ Headers with SAID token correct")


async def main():
    """Run all tests."""
    print("🚀 Testing M2M Token Injection in Robot HTTP Client")
    print("=" * 60)
    
    try:
        await test_token_injection()
        await test_auth_headers_directly()
        
        print("\n" + "=" * 60)
        print("🎯 Summary:")
        print("• M2M token is correctly retrieved from IDaaS")
        print("• Authorization header is properly set")
        print("• SAID token support works correctly")
        print("• HTTP requests include authentication headers")
        print("• Token injection flow is working as expected")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
