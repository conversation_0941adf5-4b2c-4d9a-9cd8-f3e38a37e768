# DeepHeal Monorepo

An AIOps (Artificial Intelligence for IT Operations) monorepo containing multiple services and packages for intelligent system management and healing.

## Project Structure

This monorepo contains the following projects:

- **`services/dhagent/`** - DeepHeal Agent service - A Python-based AIOps application
- **`packages/li_agentcore/`** - Li Agent Core library - Shared components and events for agent systems

## Prerequisites

- **Python 3.12** or higher
- **[uv](https://docs.astral.sh/uv/)** - Python package manager
- **Git** - Version control system

### Installing uv

If you don't have `uv` installed, you can install it using:

```bash
# On macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# On Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# Or using pip
pip install uv
```

## Development Setup

### Quick Start for New Developers

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd deepheal
   ```

2. **Install pre-commit globally (one-time setup):**
   ```bash
   uv tool install pre-commit
   ```

3. **Install pre-commit hooks:**
   ```bash
   pre-commit install
   ```

4. **Set up individual projects:**
   ```bash
   # Set up li_agentcore package
   cd packages/li_agentcore
   uv sync --group dev --group test

   # Set up dhagent service
   cd ../../services/dhagent
   uv sync --group dev --group test

   # Return to root
   cd ../..
   ```

5. **Verify the setup:**
   ```bash
   # Test pre-commit hooks
   pre-commit run --all-files
   ```

## Code Quality & Pre-commit Setup 🪝

**Status: ✅ ACTIVE** - Centralized pre-commit hooks manage code quality across all projects!

### How Pre-commit Works in This Monorepo

This repository uses a **single, centralized pre-commit configuration** at the root level that:

- ✅ **Manages all sub-projects** from one location
- ✅ **Uses project-specific Python environments** automatically via `uv run`
- ✅ **Runs appropriate tools** only on changed files in each project
- ✅ **Handles dependencies correctly** for each sub-project

### Pre-commit Hooks Overview

The following checks run automatically before each commit:

#### **Global Checks (All Files)**
- **Trailing whitespace removal**
- **End of file fixing**
- **YAML/TOML/JSON validation**
- **Merge conflict detection**
- **Large file detection**
- **Python debug statement detection**

#### **Per-Project Checks**

**For `packages/li_agentcore/`:**
- **Black**: Code formatting (88 char line length)
- **isort**: Import sorting (black profile)
- **Ruff**: Linting with auto-fixes + formatting
- **MyPy**: Type checking (src files only)
- **pytest**: Test execution using `uv run --group test`

**For `services/dhagent/`:**
- **Black**: Code formatting (88 char line length)
- **isort**: Import sorting (black profile)
- **Ruff**: Linting with auto-fixes + formatting
- **MyPy**: Type checking (src files only)
- **pytest**: Test execution using `uv run --group test`

### Why This Setup Works

1. **No Python interpreter conflicts**: Each project uses its own virtual environment via `uv run`
2. **Automatic dependency management**: `uv run --group test` ensures test dependencies are available
3. **File-based targeting**: Hooks only run on files that belong to each project
4. **Consistent tooling**: Same code quality standards across all projects
5. **Single source of truth**: One configuration file to maintain

### Pre-commit Commands

```bash
# Install hooks (one-time setup)
pre-commit install

# Run hooks on all files manually
pre-commit run --all-files

# Run hooks on staged files only
pre-commit run

# Run specific hook
pre-commit run black-dhagent

# Skip hooks for emergency commits (not recommended)
git commit --no-verify -m "emergency fix"

# Update hook repositories to latest versions
pre-commit autoupdate
```

### When Hooks Run

- **Automatically**: Every time you run `git commit`
- **File-based**: Only processes files that match each project's patterns
- **Blocking**: Commit is blocked if any hook fails
- **Auto-fixing**: Many hooks (black, ruff, isort) automatically fix issues

### Troubleshooting Pre-commit

#### Common Issues & Solutions

**Problem**: `pytest: command not found`
```bash
# Solution: Ensure test dependencies are installed
cd packages/li_agentcore && uv sync --group test
cd services/dhagent && uv sync --group test
```

**Problem**: `pre-commit: command not found`
```bash
# Solution: Install pre-commit globally
uv tool install pre-commit
```

**Problem**: Hooks are slow
```bash
# Solution: Hooks only run on changed files by default, but you can:
# 1. Run on specific files: pre-commit run --files path/to/file.py
# 2. Skip hooks temporarily: git commit --no-verify
```

**Problem**: MyPy errors in dependencies
```bash
# This is expected - the configuration ignores missing imports for external libraries
# Focus on fixing type issues in your own code
```

## Working with Individual Projects

### Li AgentCore Package

```bash
cd packages/li_agentcore

# Install dependencies
uv sync --group dev --group test

# Run tests
uv run --group test pytest

# Run specific development tools
uv run --group dev black .
uv run --group dev ruff check .
```

### DHAgent Service

```bash
cd services/dhagent

# Install dependencies
uv sync --group dev --group test

# Run the application
uv run python main.py

# Run tests
uv run --group test pytest

# Run with auto-reload
uv run uvicorn main:app --reload
```

## Contributing

### Making Changes

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** in the appropriate project directory

3. **Test locally:**
   ```bash
   # Test the specific project
   cd services/dhagent  # or packages/li_agentcore
   uv run --group test pytest
   ```

4. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: your descriptive commit message"
   # Pre-commit hooks will run automatically!
   ```

5. **Push and create a merge request:**
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Message Format

This project uses [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Test additions or modifications
- `chore:` - Maintenance tasks

### Code Review Checklist

- ✅ Pre-commit hooks pass
- ✅ Tests are included and passing
- ✅ Documentation is updated
- ✅ Commit messages follow conventional format
- ✅ No breaking changes without proper versioning

## Architecture & Design

This monorepo follows these architectural principles:

- **Clean Architecture** - Clear separation of concerns
- **Domain-Driven Design** - Business logic organization
- **Microservices** - Loosely coupled, independently deployable services
- **Shared Libraries** - Common functionality in packages

For detailed architecture information, see individual project README files.

## References

- [uv Documentation](https://docs.astral.sh/uv/)
- [Pre-commit Documentation](https://pre-commit.com/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
